<template>
  <div class="report-title">
    <h2>员工资格考试统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.statisticsDate"
                type="month"
                placeholder="选择统计月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        style="width: 100%"
      >
        <el-table-column prop="data_date_range" label="日期" fixed="left">
          <template #default="scope">
            {{ queryForm.statisticsDate.substring(0, 4) + '-01' }}~{{ queryForm.statisticsDate }}
          </template>
        </el-table-column>
        <el-table-column prop="brch_cd" label="分支机构代码" fixed="left" />
        <el-table-column prop="brch_name" label="分支机构名称" fixed="left" />
        <el-table-column prop="emp_no" label="员工编号" />
        <el-table-column prop="emp_name" label="员工姓名" />
        <el-table-column prop="fin_anl_exam" label="特许金融分析师考试(CFA)">
          <template #default="scope">
            <el-tag :type="scope.row.fin_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fin_risk_mang_exam" label="金融风险管理师考试(FRM)" >
          <template #default="scope">
            <el-tag :type="scope.row.fin_risk_mang_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fin_risk_mang_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_advr_exam" label="证券投资顾问考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_advr_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_advr_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_anl_exam" label="证券分析师考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_iq_exam" label="证券从业资格考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fnd_iq_exam" label="基金从业资格考试" >
          <template #default="scope">
            <el-tag :type="scope.row.fnd_iq_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.fnd_iq_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scr_ivsm_anl_exam" label="证券投资分析考试" >
          <template #default="scope">
            <el-tag :type="scope.row.scr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.scr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_exam" label="期货投资分析考试" >
          <template #default="scope">
            <el-tag :type="scope.row.futr_ivsm_anl_exam === '通过' ? 'success' : 'danger'">
              {{ scope.row.futr_ivsm_anl_exam }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="futr_ivsm_anl_pass_time" label="期货投资分析通过时间" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取默认日期（当前月份）
const getDefaultDate = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1 // 0-11，需要+1
  return `${currentYear}-${String(currentMonth).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  statisticsDate: getDefaultDate(),
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001',
    goBack: params.get('goback') || false
  }
}

const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}


// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.statisticsDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {}

  // 修改：只查询每年一月的数据
  filters.and = `(tect_end_date.gte.${queryForm.statisticsDate.substring(0, 4) + '-01'},tect_strt_date.lte.${queryForm.statisticsDate})`

  // 添加分支机构查询条件
  if (queryForm.branchCode) {
    filters.brch_cd = `eq.${queryForm.branchCode}`
  }

  // 添加last_flag=1条件
  // filters.last_flag = 'eq.1'

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: 'tect_strt_date.desc,brch_cd.asc,emp_no.asc'
    },
    headers: {
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`,
      'Accept-Profile': 'mkt_base'
    }
  }

  http.get('/v_emp_qlfy_exam_mtc', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 导出
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.statisticsDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加日期范围查询条件
    filters.and = `(tect_strt_date.lte.${queryForm.statisticsDate},tect_end_date.gte.${queryForm.statisticsDate.substring(0, 4) + '-01'})`

    // 添加分支机构查询条件
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
      filters.brch_cd = `in.(${branchCodesStr})`
    }

    // 添加last_flag=1条件
    // filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,brch_cd.asc,emp_no.asc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_emp_qlfy_exam_mtc', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '员工编号', '员工姓名',
        '特许金融分析师考试(CFA)', '金融风险管理师考试(FRM)', '证券投资顾问考试',
        '证券分析师考试', '证券从业资格考试', '基金从业资格考试',
        '证券投资分析考试', '期货投资分析考试', '期货投资分析通过时间'
      ],
      // 数据行
      ...allData.map(item => [
        `${queryForm.statisticsDate.substring(0, 4) + '-01'}~${queryForm.statisticsDate}`,
        item.brch_cd || '',
        item.brch_name || '',
        item.emp_no || '',
        item.emp_name || '',
        item.fin_anl_exam || '',
        item.fin_risk_mang_exam || '',
        item.scr_ivsm_advr_exam || '',
        item.scr_anl_exam || '',
        item.scr_iq_exam || '',
        item.fnd_iq_exam || '',
        item.scr_ivsm_anl_exam || '',
        item.futr_ivsm_anl_exam || '',
        item.futr_ivsm_anl_pass_time || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 20 }, // 分支机构名称
      { wch: 12 }, // 员工编号
      { wch: 15 }, // 员工姓名
      { wch: 25 }, // 特许金融分析师考试(CFA)
      { wch: 25 }, // 金融风险管理师考试(FRM)
      { wch: 20 }, // 证券投资顾问考试
      { wch: 18 }, // 证券分析师考试
      { wch: 20 }, // 证券从业资格考试
      { wch: 20 }, // 基金从业资格考试
      { wch: 20 }, // 证券投资分析考试
      { wch: 20 }, // 期货投资分析考试
      { wch: 25 }  // 期货投资分析通过时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '员工资格考试统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `员工资格考试统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
}

.query-card {
  margin-bottom: 20px;

  .query-form {
    .el-form-item {
      margin-bottom: 18px;
    }

    .button-group {
      text-align: center;
      margin-top: 10px;

      .el-button {
        margin: 0 5px;
      }
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
      white-space: nowrap;
      vertical-align: middle;
    }
  }

  .el-table__body-wrapper {
    td {
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        overflow: visible;
        white-space: normal;
        word-wrap: break-word;
      }
    }
  }
}

// 标签样式
:deep(.el-tag) {
  font-weight: 500;
}
</style>