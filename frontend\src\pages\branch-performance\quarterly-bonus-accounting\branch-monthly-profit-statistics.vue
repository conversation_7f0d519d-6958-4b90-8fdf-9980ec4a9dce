<template>
  <div class="report-title">
    <h2>分支机构月度利润统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="开始年份" required>
              <el-date-picker
                v-model="queryForm.startYear"
                type="year"
                placeholder="选择开始年份"
                format="YYYY"
                value-format="YYYY"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="截止年份" required>
              <el-date-picker
                v-model="queryForm.endYear"
                type="year"
                placeholder="选择截止年份"
                format="YYYY"
                value-format="YYYY"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="yr_no" label="年份" fixed="left" />
        <el-table-column prop="brh_code" label="分支机构代码" sortable/>
        <el-table-column prop="brh_name" label="分支机构名称" />
        <el-table-column prop="index_tgt" label="年度利润目标" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.index_tgt) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_01" label="1月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_01) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_02" label="2月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_02) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_03" label="3月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_03) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_04" label="4月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_04) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_05" label="5月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_05) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_06" label="6月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_06) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_07" label="7月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_07) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_08" label="8月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_08) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_09" label="9月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_09) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_10" label="10月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_10) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_11" label="11月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_11) }}
          </template>
        </el-table-column>
        <el-table-column prop="mon_12" label="12月" align="right" >
          <template #default="scope">
            {{ formatNumber(scope.row.mon_12) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取URL参数，参考 employee_info_statistics
const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '1')

// 获取默认年份
const getDefaultYears = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const lastYear = currentYear - 1

  return {
    startYear: lastYear.toString(),
    endYear: currentYear.toString()
  }
}

const defaultYears = getDefaultYears()

// 查询表单
const queryForm = reactive({
  startYear: defaultYears.startYear,
  endYear: defaultYears.endYear,
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || oacode.value,
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || srcsys.value,
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  })
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.startYear) {
    ElMessage.warning('请选择开始年份')
    return
  }

  if (!queryForm.endYear) {
    ElMessage.warning('请选择截止年份')
    return
  }

  // 验证年份范围
  if (queryForm.startYear > queryForm.endYear) {
    ElMessage.warning('开始年份不能大于截止年份')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: urlParams.oacode,
    srcsys: urlParams.srcsys,
    p_start_yr: queryForm.startYear,
    p_end_yr: queryForm.endYear,
    p_brh_cd: queryForm.branchCode || null
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_brh_mon_profit_s', functionParams, config)
  .then(response => {
    let rawData = response.data || []

    // 对数据进行排序（按年份、分支机构代码排序）
    rawData = rawData.sort((a, b) => {
      if (a.yr_no !== b.yr_no) {
        return a.yr_no.localeCompare(b.yr_no)
      }
      return a.brh_code.localeCompare(b.brh_code)
    })

    // 进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.startYear) {
    ElMessage.warning('请选择开始年份')
    return
  }

  if (!queryForm.endYear) {
    ElMessage.warning('请选择截止年份')
    return
  }

  // 验证年份范围
  if (queryForm.startYear > queryForm.endYear) {
    ElMessage.warning('开始年份不能大于截止年份')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: urlParams.oacode,
      srcsys: urlParams.srcsys,
      p_start_yr: queryForm.startYear,
      p_end_yr: queryForm.endYear,
      p_brh_cd: queryForm.branchCode || null
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_brh_mon_profit_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '年份', '分支机构代码', '分支机构名称', '年度利润目标', '1月', '2月', '3月', '4月',
        '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.yr_no || '',
        item.brh_code || '',
        item.brh_name || '',
        formatNumber(item.index_tgt),
        formatNumber(item.mon_01),
        formatNumber(item.mon_02),
        formatNumber(item.mon_03),
        formatNumber(item.mon_04),
        formatNumber(item.mon_05),
        formatNumber(item.mon_06),
        formatNumber(item.mon_07),
        formatNumber(item.mon_08),
        formatNumber(item.mon_09),
        formatNumber(item.mon_10),
        formatNumber(item.mon_11),
        formatNumber(item.mon_12)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 8 },  // 年份
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 年度利润目标
      { wch: 12 }, // 1月
      { wch: 12 }, // 2月
      { wch: 12 }, // 3月
      { wch: 12 }, // 4月
      { wch: 12 }, // 5月
      { wch: 12 }, // 6月
      { wch: 12 }, // 7月
      { wch: 12 }, // 8月
      { wch: 12 }, // 9月
      { wch: 12 }, // 10月
      { wch: 12 }, // 11月
      { wch: 12 }  // 12月
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构月度利润统计表')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构月度利润统计表_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}
</script>

<style lang="scss" scoped>

/* 状态标签样式 */
</style>
