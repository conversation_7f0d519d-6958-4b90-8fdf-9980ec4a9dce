import axios from 'axios'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

class RequestHttp {
  constructor(config) {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_URL,
      timeout: 5000,
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 如果配置了 Range 请求头，则自动添加 Prefer: count=exact
        if (config.headers && config.headers['Range']) {
          config.headers['Prefer'] = 'count=exact'
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器：统一处理 datetime 字段格式
    this.instance.interceptors.response.use(
      (response) => {
        // 如果是 blob 类型，直接返回
        if (response.config.responseType === 'blob') {
          return response
        }

        // 统一格式化 datetime 字段
        const formatDateTime = (data) => {
          if (Array.isArray(data)) {
            return data.map(formatDateTime)
          } else if (data && typeof data === 'object') {
            return Object.keys(data).reduce((acc, key) => {
              const value = data[key]

              // 匹配常见日期时间字段名
              if (
                key.toLowerCase().includes('time') ||
                key.toLowerCase().includes('date')
              ) {
                // 根据字符串长度判断格式
                if (value && typeof value === 'string') {
                  if (value.length === 7) { // yyyy-mm
                    acc[key] = value
                  } else if (value.length === 10) { // yyyy-mm-dd
                    acc[key] = dayjs(value).format('YYYY-MM-DD')
                  } else { // yyyy-mm-dd hh24:mi:ss
                    acc[key] = dayjs(value).format('YYYY-MM-DD HH:mm:ss')
                  }
                } else {
                  acc[key] = ''
                }
              } else if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
                acc[key] = formatDateTime(value)
              } else {
                acc[key] = value
              }

              return acc
            }, {})
          }
          return data
        }

        // 对整个响应数据进行格式化
        const formattedData = formatDateTime(response.data)

        // 自动提取 Content-Range 总数
        let total = 0
        const contentRangeHeader = response.headers['content-range'] || response.headers['Content-Range']
        if (contentRangeHeader) {
          const match = contentRangeHeader.match(/\/(\d+)$/)
          if (match && match[1]) {
            total = parseInt(match[1], 10)
          }
        }

        // 返回新的 response 对象，保留 headers 等信息
        return {
          ...response,
          data: formattedData,
          total
        }
      },
      async (error) => {
        const { response } = error
        if (error.message.indexOf('timeout') !== -1) {
          ElMessage.error('请求超时！请您稍后重试')
        }
        if (error.message.indexOf('Network Error') !== -1) {
          ElMessage.error('网络错误！请您稍后重试')
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * @description 常用请求方法封装
   */
  get(url, params = {}, config) {
    return this.instance.get(url, { params, ...config }).then(response => ({
      data: response.data,
      total: response.total
    }))
  }

  post(url, params, config) {
    return this.instance.post(url, params, config).then(response => {
      // 判断是否成功但 data 为空的情况
      if (response.status >= 200 && response.status < 300) {
        // 如果 data 是 null 或空对象，则补全 o_status 和 o_msg
        if (!response.data || Object.keys(response.data).length === 0) {
          return {
            data: { o_status: 0, o_msg: 'success' },
            total: response.total
          }
        }

        // 否则正常返回数据和状态
        return {
          data: response.data,
          total: response.total
        }
      }

      // 其他非 2xx 情况也返回错误信息
      return {
        data: { o_status: -1, o_msg: `HTTP 错误：${response.status}` },
        total: 0
      }
    })
  }


  patch(url, params, config) {
    return this.instance.patch(url, params, config).then(response => {
      // 判断是否成功但 data 为空的情况
      if (response.status >= 200 && response.status < 300) {
        // 如果 data 是 null 或空对象，则补全 o_status 和 o_msg
        if (!response.data || Object.keys(response.data).length === 0) {
          return {
            data: { o_status: 0, o_msg: 'success' },
            total: response.total
          }
        }

        // 否则正常返回数据和状态
        return {
          data: response.data,
          total: response.total
        }
      }

      // 其他非 2xx 情况也返回错误信息
      return {
        data: { o_status: -1, o_msg: `HTTP 错误：${response.status}` },
        total: 0
      }
    })
  }


  put(url, params, config) {
    return this.instance.put(url, params, config).then(response => {
      // 判断是否成功但 data 为空的情况
      if (response.status >= 200 && response.status < 300) {
        // 如果 data 是 null 或空对象，则补全 o_status 和 o_msg
        if (!response.data || Object.keys(response.data).length === 0) {
          return {
            data: { o_status: 0, o_msg: 'success' },
            total: response.total
          }
        }

        // 否则正常返回数据和状态
        return {
          data: response.data,
          total: response.total
        }
      }

      // 其他非 2xx 情况也返回错误信息
      return {
        data: { o_status: -1, o_msg: `HTTP 错误：${response.status}` },
        total: 0
      }
    })
  }


  delete(url, params, config) {
    return this.instance.delete(url, params, config).then(response => {
      // 判断是否成功但 data 为空的情况
      if (response.status >= 200 && response.status < 300) {
        // 如果 data 是 null 或空对象，则补全 o_status 和 o_msg
        if (!response.data || Object.keys(response.data).length === 0) {
          return {
            data: { o_status: 0, o_msg: 'success' },
            total: response.total
          }
        }

        // 否则正常返回数据和状态
        return {
          data: response.data,
          total: response.total
        }
      }

      // 其他非 2xx 情况也返回错误信息
      return {
        data: { o_status: -1, o_msg: `HTTP 错误：${response.status}` },
        total: 0
      }
    })
  }

  download(url, params = {}, config) {
    return this.instance.post(url, params, { ...config, responseType: 'blob' }).then(response => response)
  }

  template(url, params = {}, config) {
    return this.instance.get(url, { params, ...config, responseType: 'blob' }).then(response => response)
  }

  upload(url, params, config) {
    return this.instance.post(url, params, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }).then(response => response)
  }

  /**
   * @description 调用PostgreSQL函数的方法
   * @param {string} functionName - 函数名称
   * @param {object} params - 函数参数
   * @param {object} config - 请求配置
   * @param {boolean} wrapInRequest - 是否将参数包装在i_request中，默认为true
   * @returns {Promise} 返回函数执行结果
   */
  callFunction(functionName, params = {}, config = {}, wrapInRequest = true) {
    // 构建RPC调用URL
    const url = `/rpc/${functionName}`

    // 构建请求体
    const requestBody = wrapInRequest ? { i_request: params } : params

    return this.instance.post(url, requestBody, {
      ...config,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    }).then(response => {
      // 对于函数调用，直接返回数据数组
      if (Array.isArray(response.data)) {
        return {
          data: response.data,
          total: response.data.length
        }
      }

      // 如果返回的是包含o_status和o_msg的对象，按原有逻辑处理
      if (response.data && typeof response.data === 'object' &&
          ('o_status' in response.data || 'o_msg' in response.data)) {
        return {
          data: response.data,
          total: response.total || 0
        }
      }

      // 其他情况正常返回
      return {
        data: response.data,
        total: response.total || 0
      }
    })
  }
}

export default new RequestHttp()