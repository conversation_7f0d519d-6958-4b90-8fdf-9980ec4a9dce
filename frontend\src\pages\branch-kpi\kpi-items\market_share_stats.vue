<template>
  <div class="report-title">
    <h2>成交额市占统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.dataDate"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分支机构">
              <el-select
                v-model="queryForm.branchCode"
                placeholder="请输入分支机构代码或名称进行搜索"
                filterable
                remote
                reserve-keyword
                :remote-method="remoteSearchBranch"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="`${item.value} - ${item.label}`"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right;">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="warning" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table :data="tableData" border stripe v-loading="loading" table-layout="auto">
        <el-table-column prop="data_date_range" label="日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码" />
        <el-table-column prop="brh_name" label="分支机构名称" />
        <el-table-column prop="last_year_market_share" label="去年成交额市占" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.last_year_market_share) }}
          </template>
        </el-table-column>
        <el-table-column prop="current_market_share" label="档期成交额市占" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.current_market_share) }}
          </template>
        </el-table-column>
        <el-table-column prop="market_share_yoy" label="成交额市占同比" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.market_share_yoy) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

const route = useRoute()

// 查询表单
const queryForm = reactive({
  dataDate: '',
  branchCode: '',
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 分支机构列表
const branchOptions = ref([])

const oacode = ref(route.query.oacode || 'system')
const srcsys = ref(route.query.srcsys || '')

// 获取分支机构选项
const loadBranchOptions = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'public'
      }
    }

    const response = await http.get('/dc$org', {}, config)
    const data = response.data || []

    branchOptions.value = data.map(item => ({
      value: item.department_id,
      label: item.department_nam
    }))
  } catch (error) {
    console.error('获取分支机构选项失败:', error)
    // 如果API失败，设置空数组
    branchOptions.value = []
    ElMessage.warning('获取分支机构选项失败')
  }
}

// 远程搜索分支机构
const remoteSearchBranch = async (query) => {
  if (query) {
    try {
      const config = {
        params: {
          or: `(department_id.ilike.*${query}*,department_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Accept-Profile': 'public'
        }
      }

      const response = await http.get('/dc$org', {}, config)
      const data = response.data || []

      branchOptions.value = data.map(item => ({
        value: item.department_id,
        label: item.department_nam
      }))
    } catch (error) {
      console.error('搜索分支机构失败:', error)
    }
  } else {
    loadBranchOptions()
  }
}

// 表格数据
const tableData = ref([])

// 初始化数据
onMounted(async () => {
  // 加载分支机构选项
  await loadBranchOptions()

  // 获取上个月的日期
  const lastMonth = new Date()
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  const year = lastMonth.getFullYear()
  const month = String(lastMonth.getMonth() + 1).padStart(2, '0')
  queryForm.dataDate = `${year}-${month}`

  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询方法
const handleQuery = async () => {
  if (!queryForm.dataDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 构建函数参数
  const functionParams = {
    oacode: oacode.value,
    srcsys: srcsys.value,
    p_data_date: queryForm.dataDate,
    p_brh_cd: queryForm.branchCode,
    p_page_num: currentPage.value,
    p_page_size: pageSize.value
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_market_share_stats_s', functionParams, config)
  .then(response => {
    const rawData = response.data || []

    // 由于函数已经进行了汇总，直接使用返回的数据
    // 但需要进行分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    const paginatedData = rawData.slice(startIndex, endIndex)

    tableData.value = paginatedData
    totalCount.value = rawData.length
  })
  .catch(error => {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  })
  .finally(() => {
    lastSuccessfulForm = JSON.stringify(queryForm);
    loading.value = false
  })
}

// 导出方法
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      p_data_date: queryForm.dataDate,
      p_brh_cd: queryForm.branchCode,
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_market_share_stats_s', functionParams, config)
    const aggregatedData = response.data || []

    if (aggregatedData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '去年成交额市占', '档期成交额市占', '成交额市占同比'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date_range ?? '',
        item.brh_cd ?? '',
        item.brh_name ?? '',
        formatNumber(item.last_year_market_share) ?? '',
        formatNumber(item.current_market_share) ?? '',
        formatNumber(item.market_share_yoy) ?? ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 去年成交额市占
      { wch: 15 }, // 档期成交额市占
      { wch: 15 }, // 成交额市占同比
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '成交额市占统计')

    // 生成文件名
    const now = new Date()
    const fileName = `成交额市占统计_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 格式化数字，保留4位小数
const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toFixed(4) + '%'
}
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>