<template>
  <div class="dashboard-container">
    <!-- 第一部分：4个指标卡片 -->
    <el-card class="section-header-card">
      <div class="section-title">考核完成进度</div>
    </el-card>
    <el-row :gutter="20" class="section">
      <el-col :span="6" v-for="(item, index) in profitCards" :key="item.title">
        <el-card class="profit-card">
          <el-row class="first-line">
            <el-col :span="16" align="left">
              <span class="title" >{{ item.title }}</span>
            </el-col>
            <el-col :span="8" style="text-align: right;">
              <span class="score">指标考核得分:{{ item.score }}</span>
            </el-col>
          </el-row>
          <el-row class="second-line">
            <el-col :span="12" align="left">
              <span class="amount" :style="{ color: item.growth > 0 ? 'red' : 'green' }">{{ item.amount }}元</span>
              <span class="growth" :class="{ positive: item.growth > 0 }">
                (同比{{ item.growth > 0 ? '+' : '' }}{{ item.growth }}%)
              </span>
            </el-col>
            <el-col :span="12" style="text-align: right;">
              <span>
                完成进度 {{ item.progress }}%
              </span>
            </el-col>
          </el-row>
          <el-row class="second-line">
            <el-col :span="16" align="left">
              <span class="title" >{{ item.goal }}</span>
            </el-col>
           
          </el-row>
          <el-row class="second-line">
            <el-col :span="12" align="left">
              <span class="amount">{{ item.goal_amount }}元</span>
            </el-col>
          
          </el-row>
          <!-- <el-row class="third-line">
            <el-col :span="24">
              <span>{{ item.goal }}</span>
            </el-col>
          </el-row> -->
          <el-row class="fourth-line">
            <el-col :span="24">
              <el-tooltip 
                :content="`年度目标: ${item.goal_amount}, 完成进度: ${item.amount}`" 
                placement="top"
                :show-after="200"
              >
                <el-progress 
                  :percentage="item.progress"
                  :color="processColors"
                  :show-text="true"
                  :stroke-width="20"
                  style="margin-top: 10px;"
                />
              </el-tooltip>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第二部分：分支机构排名 -->
    <el-card class="section-header-card">
      <div class="section-title">分支机构排名</div>
    </el-card>
    <el-row :gutter="20" class="section">
      <el-col :span="8" v-for="ranking in branchRankings" :key="ranking.id">
        <el-card class="ranking-card">
          <div class="ranking-title">{{ ranking.title }}</div>
          <el-table :data="ranking.data" style="width: 100%" height="250">
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="branch" label="分支机构名称" align="center" />
            <el-table-column prop="score" label="得分" width="120" align="right" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 第三部分：趋势图 -->
    <el-card class="section-header-card">
      <div class="section-title">趋势分析</div>
    </el-card>
    <el-row class="section">
      <el-card class="trend-card">
        <el-row :gutter="20">
          <el-col :span="6" v-for="(trend, index) in trendCharts" :key="trend.title">
            <el-card class="trend-item">
              <div class="trend-title">{{ trend.title }}</div>
              <div :ref="el => setTrendChartRef(el, index)" class="trend-chart"></div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'


// 进度条自定义颜色
const processColors = [
  { color: '#F05025', percentage: 50 },
  { color: '#5B7BFB', percentage: 80 },
  { color: '#16D585', percentage: 100 }
]

// 第一部分数据
const profitCards = ref([
  {
    title: '累计利润',
    score: 35,
    amount: '10003',
    goal_amount: '30000',
    growth: 5,
    progress: 100,
    goal: '年度利润目标'
  },
  {
    title: '收入',
    score: 30,
    amount: '15000',
    goal_amount: '30000',
    growth: 8,
    progress: 65,
    goal: '年度收入目标'
  },
  {
    title: '日均权益',
    score: 25,
    amount: '8000',
    goal_amount: '30000',
    growth: -2,
    progress: 55,
    goal: '年度权益目标'
  },
  {
    title: '新增有效户',
    score: 20,
    amount: '120',
    goal_amount: '30000',
    growth: 12,
    progress: 45,
    goal: '年度客户目标'
  }
])

// 第二部分数据 - 分支机构排名
const branchRankings = ref([
  {
    id: 1,
    title: '考核得分排名',
    data: [
      { rank: 1, branch: '北京分公司', score: 95 },
      { rank: 2, branch: '上海分公司', score: 92 },
      { rank: 3, branch: '广州分公司', score: 88 },
      { rank: 4, branch: '深圳分公司', score: 85 },
      { rank: 5, branch: '杭州分公司', score: 82 }
    ]
  },
  {
    id: 2,
    title: '利润排名',
    data: [
      { rank: 1, branch: '成都分公司', score: 90 },
      { rank: 2, branch: '武汉分公司', score: 87 },
      { rank: 3, branch: '西安分公司', score: 84 },
      { rank: 4, branch: '南京分公司', score: 81 },
      { rank: 5, branch: '重庆分公司', score: 78 }
    ]
  },
  {
    id: 3,
    title: '收入排名',
    data: [
      { rank: 1, branch: '天津分公司', score: 89 },
      { rank: 2, branch: '青岛分公司', score: 86 },
      { rank: 3, branch: '大连分公司', score: 83 },
      { rank: 4, branch: '厦门分公司', score: 80 },
      { rank: 5, branch: '宁波分公司', score: 77 }
    ]
  },
  {
    id: 4,
    title: '日均权益排名',
    data: [
      { rank: 1, branch: '天津分公司', score: 89 },
      { rank: 2, branch: '青岛分公司', score: 86 },
      { rank: 3, branch: '大连分公司', score: 83 },
      { rank: 4, branch: '厦门分公司', score: 80 },
      { rank: 5, branch: '宁波分公司', score: 77 }
    ]
  },
  {
    id: 5,
    title: '新增有效户排名',
    data: [
      { rank: 1, branch: '天津分公司', score: 89 },
      { rank: 2, branch: '青岛分公司', score: 86 },
      { rank: 3, branch: '大连分公司', score: 83 },
      { rank: 4, branch: '厦门分公司', score: 80 },
      { rank: 5, branch: '宁波分公司', score: 77 }
    ]
  }
])

// 第三部分数据 - 趋势图
const trendCharts = ref([
  {
    title: '利润趋势',
    data: [
      { month: '1月', value: 120, lastYear: 100 },
      { month: '2月', value: 130, lastYear: 110 },
      { month: '3月', value: 140, lastYear: 125 },
      { month: '4月', value: 150, lastYear: 130 },
      { month: '5月', value: 160, lastYear: 135 },
      { month: '6月', value: 170, lastYear: 140 }
    ]
  },
  {
    title: '收入趋势',
    chartRef: null,
    data: [
      { month: '1月', value: 200, lastYear: 180 },
      { month: '2月', value: 220, lastYear: 190 },
      { month: '3月', value: 240, lastYear: 210 },
      { month: '4月', value: 260, lastYear: 230 },
      { month: '5月', value: 280, lastYear: 250 },
      { month: '6月', value: 300, lastYear: 270 }
    ]
  },
  {
    title: '日均权益趋势',
    chartRef: null,
    data: [
      { month: '1月', value: 80, lastYear: 70 },
      { month: '2月', value: 85, lastYear: 75 },
      { month: '3月', value: 90, lastYear: 80 },
      { month: '4月', value: 95, lastYear: 85 },
      { month: '5月', value: 100, lastYear: 90 },
      { month: '6月', value: 105, lastYear: 95 }
    ]
  },
  {
    title: '新增有效户趋势',
    chartRef: null,
    data: [
      { month: '1月', value: 30, lastYear: 25 },
      { month: '2月', value: 35, lastYear: 28 },
      { month: '3月', value: 40, lastYear: 32 },
      { month: '4月', value: 45, lastYear: 36 },
      { month: '5月', value: 50, lastYear: 40 },
      { month: '6月', value: 55, lastYear: 45 }
    ]
  }
])

// 趋势图表引用数组
const trendChartRefs = ref([])

// 设置趋势图表引用
const setTrendChartRef = (el, index) => {
  if (el) {
    trendChartRefs.value[index] = el
  }
}

// 初始化趋势图
const initTrendCharts = () => {
  trendCharts.value.forEach((trend, index) => {
    nextTick(() => {
      const chartDom = trendChartRefs.value[index]
      if (chartDom) {
        const chart = echarts.init(chartDom)
        
        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['本期', '去年同期']
          },
          xAxis: {
            type: 'category',
            data: trend.data.map(item => item.month)
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '本期',
              type: 'bar',
              barGap: 0,
              data: trend.data.map(item => item.value),
              itemStyle: {
                color: '#5470c6'
              }
            },
            {
              name: '去年同期',
              type: 'bar',
              data: trend.data.map(item => item.lastYear),
              itemStyle: {
                color: '#91cc75'
              }
            }
          ]
        }
        
        chart.setOption(option)
      }
    })
  })
}

// 组件挂载后初始化图表
onMounted(() => {
  initTrendCharts()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.section-header-card {
  margin-bottom: 15px;
  border-left: 4px solid #409eff;
    
  .section-title {
    font-size: 20px;
    font-weight: bold;
    color: #303133;
    padding: 5px 0;
    text-align: left;
  }
}

.section {
  margin-bottom: 30px;
}

.profit-card {
  height: 200px;
  
  .card-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .first-line {
    display: flex;
    justify-content: space-between;
    
    .title {
      font-size: 16px;
      font-weight: bold;
    }
    
    .score {

      font-weight: bold;
    }
  }
  
  .second-line {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    
    .amount {
      font-size: 18px;
      font-weight: bold;
    }
    
    .growth {
      color: #f56c6c;
      
      &.positive {
        color: #67c23a;
      }
    }
  }
  
  .third-line {
    color: #909399;
    font-size: 14px;
  }
  
  .fourth-line {
    margin-top: 10px;
  }
}

.ranking-card {
  width: 100%;
  margin: 10px 10px;
  //padding: 0 20px;
  
  .ranking-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #303133;
    text-align: left;;
  }
}

.trend-card {
  width: 100%;
  
  .trend-item {
    height: 350px;
    
    .trend-title {
      text-align: center;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .trend-chart {
      width: 100%;
      height: 300px;
    }
  }
}
</style>
