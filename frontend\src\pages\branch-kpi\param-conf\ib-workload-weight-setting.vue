<template>
  <div class="report-title">
    <h2>IB工作量权重设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="工作属性">
              <el-select
                v-model="queryForm.workAttrs"
                multiple
                clearable
                placeholder="请选择工作属性"
              >
                <el-option
                  v-for="item in workAttrOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="work_attr_name" label="工作属性" />
        <el-table-column prop="exam_wght" label="考核权重" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.exam_wght) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="info" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="生效开始日期" prop="tect_strt_date">
          <el-date-picker
            v-model="formData.tect_strt_date"
            type="month"
            placeholder="选择开始月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生效结束日期" prop="tect_end_date">
          <el-date-picker
            v-model="formData.tect_end_date"
            type="month"
            placeholder="选择结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="工作属性" prop="work_attr">
          <el-select
            v-model="formData.work_attr"
            placeholder="请选择工作属性"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in workAttrOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考核权重" prop="exam_wght">
          <el-input-number
            v-model="formData.exam_wght"
            :precision="0"
            :step="1"
            :min="0"
            placeholder="请输入考核权重"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      title="查看历史记录"
      v-model="historyDialogVisible"
      width="1000px"
    >
      <el-table
        :data="historyData"
        border
        stripe
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="work_attr_name" label="工作属性" />
        <el-table-column prop="exam_wght" label="考核权重" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.exam_wght) }}
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 查询表单
const queryForm = reactive({
  workAttrs: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 工作属性选项
const workAttrOptions = ref([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 表单
const formRef = ref()
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  work_attr: '',
  work_attr_name: '',
  exam_wght: null,
  uuid: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  work_attr: [
    { required: true, message: '请选择工作属性', trigger: 'change' }
  ],
  exam_wght: [
    { required: true, message: '请输入考核权重', trigger: 'blur' }
  ]
}

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 格式化数字
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString('zh-CN')
}

// 获取工作属性选项
const loadWorkAttrOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0004',
        deleted: `eq.0`
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    workAttrOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取工作属性选项失败:', error)
    workAttrOptions.value = []
    ElMessage.warning('获取工作属性选项失败')
  }
}

// 初始化数据
onMounted(() => {
  loadWorkAttrOptions()
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }
    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.workAttrs && queryForm.workAttrs.length > 0) {
      const workAttrsStr = queryForm.workAttrs.map(attr => `"${attr}"`).join(',')
      filters.work_attr = `in.(${workAttrsStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_ib_wkld_wght_set', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增IB工作量权重'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改IB工作量权重'
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, row)

  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该工作属性的所有历史记录
    const config = {
      params: {
        work_attr: `eq.${row.work_attr}`,
        order: 'crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get('/v_ib_wkld_wght_set', {}, config)
    const data = response.data || []

    // 为每条记录添加工作属性名称显示
    historyData.value = data.map(item => {
      const workAttrOption = workAttrOptions.value.find(option => option.value === item.work_attr)
      return {
        ...item,
        work_attr_name: workAttrOption ? workAttrOption.label : item.work_attr
      }
    })
    historyDialogVisible.value = true

  } catch (error) {
    console.error('获取历史记录时发生错误:', error)
    ElMessage.error('获取历史记录失败，请检查网络连接')
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.workAttrs && queryForm.workAttrs.length > 0) {
      const workAttrsStr = queryForm.workAttrs.map(attr => `"${attr}"`).join(',')
      filters.work_attr = `in.(${workAttrsStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_ib_wkld_wght_set', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 为每条记录添加工作属性名称显示
    const exportData = allData.map(item => {
      const workAttrOption = workAttrOptions.value.find(option => option.value === item.work_attr)
      return {
        ...item,
        work_attr_name: workAttrOption ? workAttrOption.label : item.work_attr
      }
    })

    // 准备导出数据
    const excelData = [
      // 表头
      ['生效开始日期', '生效结束日期', '工作属性', '考核权重', '创建时间', '更新时间'],
      // 数据行
      ...exportData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.work_attr_name || '',
        formatNumber(item.exam_wght),
        formatDateTime(item.crt_time),
        formatDateTime(item.upd_time)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 工作属性
      { wch: 12 }, // 考核权重
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'IB工作量权重设置')

    // 生成文件名
    const now = new Date()
    const fileName = `IB工作量权重设置_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacde,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            work_attr: formData.work_attr,
            exam_wght: formData.exam_wght
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await http.post('/rpc/p_ib_wkld_wght_set_e', requestData, {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        })

        if (response.data && response.data.o_status === 0) {
          const result = response.data

          if (result.o_status === 0) {
            ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
            dialogVisible.value = false
            handleQuery() // 重新查询数据
          } else {
            ElMessage.error(result.o_msg || '操作失败')
          }
        } else {
          ElMessage.error(response.data?.o_msg)
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacde,
        uuid: row.uuid
      }
    }

    const response = await http.post('/rpc/p_ib_wkld_wght_set_e', requestData, {
      headers: {
        'Content-Profile': 'mkt_base',
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.o_status === 0) {
      const result = response.data

      if (result.o_status === 0) {
        ElMessage.success('删除成功')
        handleQuery() // 重新查询数据
      } else {
        ElMessage.error(result.o_msg || '删除失败')
      }
    } else {
      ElMessage.error('删除失败，请检查网络连接')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    work_attr: '',
    work_attr_name: '',
    exam_wght: null,
    uuid: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style lang="scss" scoped>
/* empty */
</style>
