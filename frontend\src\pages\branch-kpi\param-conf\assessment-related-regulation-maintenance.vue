<template>
  <div class="report-title">
    <h2>考核相关制度维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="制度类型">
              <el-select
                v-model="queryForm.regTypes"
                multiple
                clearable
                placeholder="请选择制度类型"
              >
                <el-option
                  v-for="item in regTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="reg_type_name" label="制度类型" />
        <el-table-column prop="reg_name" label="制度名称" />
        <el-table-column prop="file_name" label="文件名">
          <template #default="scope">
            <el-link type="primary" @click="handleView(scope.row)" v-if="scope.row.file_name">
              {{ scope.row.file_name }}
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="info" size="small" @click="handleHistory(scope.row)">历史记录</el-button>
            <el-button type="success" size="small" @click="handleDownload(scope.row)">下载</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="previewDialogVisible" :title="currentRow?.file_name ? currentRow.file_name.slice(0, 20) + (currentRow.file_name.length > 20 ? '...' : '') : '文件预览'" width="80%">
        <!-- <div v-html="previewHtml"></div> -->
        <div ref="previewContainer" class="docx-preview-container"></div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="previewDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="handleDownload(currentRow)">下载</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="生效开始日期" prop="tect_strt_date">
          <el-date-picker
            v-model="formData.tect_strt_date"
            type="month"
            placeholder="选择开始月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生效结束日期" prop="tect_end_date">
          <el-date-picker
            v-model="formData.tect_end_date"
            type="month"
            placeholder="选择结束月份"
            format="YYYY-MM"
            value-format="YYYY-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="制度类型" prop="reg_type">
          <el-select
            v-model="formData.reg_type"
            placeholder="请选择制度类型"
            :disabled="isEdit"
            style="width: 100%"
          >
            <el-option
              v-for="item in regTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="制度名称" prop="reg_name">
          <el-input
            v-model="formData.reg_name"
            placeholder="请输入制度名称"
          />
        </el-form-item>
            <el-form-item label="上传附件" prop="file_path">
              <el-upload
                ref="uploadRef"
                class="upload-demo"
                drag
                :auto-upload="false"
                :limit="1"
                accept=".docx,.doc"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :before-upload="beforeUpload"
                :file-list="fileList"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传 docx/doc 文件，且不超过 10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog
      title="查看历史记录"
      v-model="historyDialogVisible"
      width="1200px"
    >
      <el-table
        :data="historyData"
        border
        stripe
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="reg_type_name" label="制度类型" />
        <el-table-column prop="reg_name" label="制度名称" />
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, Loading, Warning } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'
import * as docx from 'docx-preview'

// 查询表单
const queryForm = reactive({
  regTypes: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 制度类型选项
const regTypeOptions = ref([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const submitLoading = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 文件预览对话框
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const previewError = ref('')
const currentRow = ref(null)
const previewContainer = ref(null)



// 表单和上传
const formRef = ref()
const uploadRef = ref()

const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  reg_type: '',
  reg_name: '',
  file_path: '',
  file_name: '',
  file_content: null,
  uuid: null
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  reg_type: [
    { required: true, message: '请选择制度类型', trigger: 'change' }
  ],
  reg_name: [
    { required: true, message: '请输入制度名称', trigger: 'blur' }
  ],
  file_path: [
    { required: true, message: '请上传附件', trigger: 'change' }
  ]
}

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacde: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001'
  }
}

// 文件上传相关
const fileList = ref([])
const urlParams = getUrlParams()

// 获取制度类型选项
const loadRegTypeOptions = async () => {
  try {
    const config = {
      params: {
        dict_code: 'eq.zxkh0005',
        deleted: `eq.0`
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/dictionary_cfg', {}, config)
    const data = response.data || []

    regTypeOptions.value = data.map(item => ({
      value: item.item_code,
      label: item.item_value
    }))
  } catch (error) {
    console.error('获取制度类型选项失败:', error)
    regTypeOptions.value = []
    ElMessage.warning('获取制度类型选项失败')
  }
}

// 初始化数据
onMounted(() => {
  loadRegTypeOptions()
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.regTypes && queryForm.regTypes.length > 0) {
      const regTypesStr = queryForm.regTypes.map(type => `"${type}"`).join(',')
      filters.reg_type = `in.(${regTypesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_exam_rela_reg_mtc', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增考核相关制度维护'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改考核相关制度维护'
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, row)

  // 如果有文件，设置文件列表显示
  if (row.file_name) {
    fileList.value = [{
      name: row.file_name,
      status: 'success'
    }]
  }

  // 修改时不需要重新上传文件
  // formRules.file_path[0].required = false

  dialogVisible.value = true
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isDoc = file.type === 'application/msword' ||
                file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isDoc) {
    ElMessage.error('只能上传 doc/docx 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 文件选择变化
const handleFileChange = (file) => {
  if (file.raw) {
    const reader = new FileReader()
    reader.onload = (e) => {
      // 将文件内容转换为base64
      const base64Content = e.target.result
      formData.file_content = base64Content
      formData.file_name = file.name
      formData.file_path = file.name

      // 更新文件列表显示
      fileList.value = [file]

      // 触发表单验证
      if (formRef.value) {
        formRef.value.validateField('file_path')
      }
    }
    reader.readAsDataURL(file.raw)
  }
}

// 文件移除
const handleFileRemove = () => {
  formData.file_content = null
  formData.file_name = ''
  formData.file_path = ''
  fileList.value = []
}

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该制度的所有历史记录
    const config = {
      params: {
        reg_type: `eq.${row.reg_type}`,
        order: 'crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get('/v_exam_rela_reg_mtc', {}, config)
    const data = response.data || []

    // 为每条记录添加制度类型名称显示
    historyData.value = data.map(item => {
      const regTypeOption = regTypeOptions.value.find(option => option.value === item.reg_type)
      return {
        ...item,
        reg_type_name: regTypeOption ? regTypeOption.label : item.reg_type
      }
    })
    historyDialogVisible.value = true

  } catch (error) {
    console.error('获取历史记录时发生错误:', error)
    ElMessage.error('获取历史记录失败，请检查网络连接')
  }
}

// 下载文件
const handleDownload = (row) => {
  const base64Data = row.file_content.split(',')[1] || row.file_content;
  const fileName = row.file_name;

  // Decode Base64 to binary string
  const binaryString = atob(base64Data);

  // Convert binary string to Uint8Array
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob from the Uint8Array
  const blob = new Blob([bytes], { type: 'application/msword' }); // 假设是 Word 文档

  // Create a download link
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;

  // Append the link to the document and trigger the download
  document.body.appendChild(link);
  link.click();

  // Remove the link from the document
  document.body.removeChild(link);
};



// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.regTypes && queryForm.regTypes.length > 0) {
      const regTypesStr = queryForm.regTypes.map(type => `"${type}"`).join(',')
      filters.reg_type = `in.(${regTypesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_exam_rela_reg_mtc', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 为每条记录添加制度类型名称显示
    const exportData = allData.map(item => {
      const regTypeOption = regTypeOptions.value.find(option => option.value === item.reg_type)
      return {
        ...item,
        reg_type_name: regTypeOption ? regTypeOption.label : item.reg_type
      }
    })

    // 准备导出数据
    const excelData = [
      // 表头
      ['生效开始日期', '生效结束日期', '制度类型', '制度名称', '文件名', '创建时间', '更新时间'],
      // 数据行
      ...exportData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.reg_type_name || '',
        item.reg_name || '',
        item.file_name || '',
        item.crt_time,
        item.upd_time
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 20 }, // 制度类型
      { wch: 30 }, // 制度名称
      { wch: 30 }, // 文件名
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '考核相关制度维护')

    // 生成文件名
    const now = new Date()
    const fileName = `考核相关制度维护_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacde,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            reg_type: formData.reg_type,
            reg_name: formData.reg_name,
            file_path: formData.file_path,
            file_name: formData.file_name,
            file_content: formData.file_content
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await http.post('/rpc/p_exam_rela_reg_mtc_e', requestData, {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        })

        if (response.data) {
          const result = response.data

          if (result.o_status === 0) {
            ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
            dialogVisible.value = false
            handleQuery() // 重新查询数据
          } else {
            ElMessage.error(result.o_msg || '操作失败')
          }
        } else {
          ElMessage.error(response.data?.o_msg)
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacde,
        uuid: row.uuid
      }
    }

    const response = await http.post('/rpc/p_exam_rela_reg_mtc_e', requestData, {
      headers: {
        'Content-Profile': 'mkt_base',
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.o_status === 0) {
      const result = response.data

      if (result.o_status === 0) {
        ElMessage.success('删除成功')
        handleQuery() // 重新查询数据
      } else {
        ElMessage.error(result.o_msg || '删除失败')
      }
    } else {
      ElMessage.error('删除失败，请检查网络连接')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    reg_type: '',
    reg_name: '',
    file_path: '',
    file_name: '',
    file_content: null,
    uuid: null
  })

  fileList.value = []
  // 重置文件验证规则
  formRules.file_path[0].required = true
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 查看详情
const handleView = async (row) => {
  currentRow.value = row
  previewDialogVisible.value = true
  previewLoading.value = true
  previewError.value = ''

  if (row.file_content) {
    try {
      // 提取 Base64 数据
      const base64Data = row.file_content.split(',')[1] || row.file_content

      // 解码 Base64 到 Uint8Array
      const binaryString = atob(base64Data)
      const len = binaryString.length
      const bytes = new Uint8Array(len)
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }

      // 创建 ArrayBuffer
      const arrayBuffer = bytes.buffer

      // 等待 DOM 更新
      await nextTick()

      const container = previewContainer.value
      if (!container) {
        throw new Error('预览容器未找到')
      }

      container.innerHTML = '' // 清空旧内容

      // 使用 docx-preview 渲染文档
      await docx.renderAsync(arrayBuffer, container, null, {
        className: "docx",
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        useBase64URL: false,
        useMathMLPolyfill: false,
        showChanges: false,
        debug: false
      })

      console.log('文档渲染成功')

    } catch (error) {
      console.error('文件预览失败:', error)
      previewError.value = '文件预览失败，可能是文件格式不支持或文件已损坏'
    } finally {
      previewLoading.value = false
    }
  } else {
    previewError.value = '文件内容为空'
    previewLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
/* empty */

.docx-preview-container {
  max-height: calc(100vh - 200px); // 根据实际布局调整
  overflow-y: auto;
}

.docx-preview-container::-webkit-scrollbar {
  width: 8px;
}

.docx-preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.docx-preview-container {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.docx-preview p,
.docx-preview h1,
.docx-preview h2,
.docx-preview h3,
.docx-preview h4,
.docx-preview h5,
.docx-preview h6 {
  margin-top: 0;
  margin-bottom: 1em;
}

.docx-preview table {
  border-collapse: collapse;
  width: 100%;
}

.docx-preview td,
.docx-preview th {
  border: 1px solid #ccc;
  padding: 8px;
}
</style>
