<template>
  <div class="report-title">
    <h2>其他加减分统计表</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计日期" required>
              <el-date-picker
                v-model="queryForm.statisticsDate"
                type="month"
                placeholder="选择统计月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                :clearable="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 使用分支机构选择器组件 -->
            <BranchSelector @branch-selected="handleBranchSelected" />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        style="width: 100%"
      >
        <el-table-column prop="data_date_range" label="日期" fixed="left" />
        <el-table-column prop="brch_cd" label="分支机构代码" fixed="left" />
        <el-table-column prop="brch_name" label="分支机构名称" fixed="left" />
        <el-table-column prop="aasp_type" label="加减分类型" />
        <el-table-column prop="aasp_val" label="加减分值" align="right">
          <template #default="scope">
              {{ formatNumber(scope.row.aasp_val) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 导入分支机构选择器组件
import BranchSelector from '~/components/BranchSelector.vue'

const router = useRouter()
const route = useRoute()

// 获取默认日期（当前月份）
const getDefaultDate = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1 // 0-11，需要+1
  return `${currentYear}-${String(currentMonth).padStart(2, '0')}`
}

// 查询表单
const queryForm = reactive({
  statisticsDate: getDefaultDate(),
  branchCode: ''
})

// 监听分支机构选择器组件的选中值变化
const handleBranchSelected = (selectedBranch) => {
  queryForm.branchCode = selectedBranch
}

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001',
    srcsys: params.get('srcsys') || '1',
    goBack: params.get('goback') || false
  }
}

const urlParams = getUrlParams()
const oacode = computed(() => urlParams.oacode)
const srcsys = computed(() => urlParams.srcsys)

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  // 验证必填字段
  if (!queryForm.statisticsDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建函数参数
  const functionParams = {
    oacode: oacode.value,
    srcsys: srcsys.value,
    p_data_date: queryForm.statisticsDate,
    p_brh_cd: queryForm.branchCode
  }

  const config = {
    headers: {
      'Accept': 'application/json',
      'Content-Profile': 'mkt_base'
    }
  }

  http.callFunction('p_audt_oth_aasp_s', functionParams, config)
    .then(response => {
      const rawData = response.data || []

      // 由于函数已经进行了汇总，直接使用返回的数据
      // 但需要进行分页处理
      const startIndex = (currentPage.value - 1) * pageSize.value
      const endIndex = startIndex + pageSize.value
      const paginatedData = rawData.slice(startIndex, endIndex)

      tableData.value = paginatedData
      totalCount.value = rawData.length
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 导出
const handleExport = async () => {
  // 验证必填字段
  if (!queryForm.statisticsDate) {
    ElMessage.warning('请选择统计日期')
    return
  }

  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建函数参数
    const functionParams = {
      oacode: oacode.value,
      srcsys: srcsys.value,
      p_data_date: queryForm.statisticsDate,
      p_brh_cd: queryForm.branchCode
    }

    const config = {
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.callFunction('p_audt_oth_aasp_s', functionParams, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '分支机构代码', '分支机构名称', '加减分类型', '加减分值'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date_range || '',
        item.brch_cd || '',
        item.brch_name || '',
        item.aasp_type || '',
        item.aasp_val || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 分支机构代码
      { wch: 20 }, // 分支机构名称
      { wch: 20 }, // 加减分类型
      { wch: 15 }  // 加减分值
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '其他加减分')

    // 生成文件名
    const now = new Date()
    const fileName = `其他加减分_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
}

.query-card {
  margin-bottom: 20px;

  .query-form {
    .el-form-item {
      margin-bottom: 18px;
    }

    .button-group {
      text-align: center;
      margin-top: 10px;

      .el-button {
        margin: 0 5px;
      }
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
      white-space: nowrap;
      vertical-align: middle;
    }
  }

  .el-table__body-wrapper {
    td {
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        overflow: visible;
        white-space: normal;
        word-wrap: break-word;
      }
    }
  }
}

// 加减分值样式
.positive {
  color: #67C23A;
  font-weight: bold;
}

.negative {
  color: #F56C6C;
  font-weight: bold;
}
</style>
