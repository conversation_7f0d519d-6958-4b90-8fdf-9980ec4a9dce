<template>
  <div class="report-title">
    <h2>分支机构绩效奖金计提比例维护</h2>
    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="生效开始日期">
              <el-date-picker
                v-model="queryForm.tect_strt_date"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="生效结束日期">
              <el-date-picker
                v-model="queryForm.tect_end_date"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
<!--              <el-button type="warning" @click="handleExport">导出</el-button>-->
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        table-layout="auto"
        v-loading="loading"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="tgt_p_comp_rate" label="目标利润完成率" />
        <el-table-column prop="quat_rew_draw_rati" label="季度绩效奖金计提比例" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button
              type="success"
              size="small"
              @click="handleHistory(scope.row)"
              >历史记录</el-button
            >
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1000px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="200px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-date-picker
                v-model="formData.tect_strt_date"
                type="month"
                placeholder="选择生效开始日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择生效结束日期"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目标利润完成率下限（%）" prop="tgt_p_comp_rate_lowl">
              <el-input
                :disabled="isEdit"
                v-model="formData.tgt_p_comp_rate_lowl"
                type="number"
                :min="0"
                :max="100"
                :step="1"
                placeholder="请输入0-100的正整数"
                :precision="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标利润完成率上限（%）" prop="tgt_p_comp_rate_topl">
              <el-input
                :disabled="isEdit"
                v-model="formData.tgt_p_comp_rate_topl"
                type="number"
                :min="0"
                :max="100"
                :step="1"
                placeholder="请输入0-100的正整数"
                :precision="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="季度绩效奖金计提比例" prop="quat_rew_draw_rati">
              <el-input
                v-model="formData.quat_rew_draw_rati"
                type="number"
                :min="0"
                :max="100"
                :step="1"
                placeholder="请输入0-100的正整数"
                :precision="0"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="历史记录" width="1200px">
      <el-table :data="historyData"
                border
                stripe
                table-layout="auto">
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="tgt_p_comp_rate_lowl" label="目标利润完成率下限" />
        <el-table-column prop="tgt_p_comp_rate_topl" label="目标利润完成率上限" />
        <el-table-column prop="quat_rew_draw_rati" label="季度绩效奖金计提比例" />
        <el-table-column prop="crt_time" label="更新时间" />
        <el-table-column prop="upd_time" label="更新时间" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="historyDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getUrlParams } from "~/utils/format.js";
import { format } from "date-fns";
import { now } from "@vueuse/core";
import * as XLSX from "xlsx";
import http from "~/http/http.js";
import Decimal from 'decimal.js'

// 查询表单
const queryForm = reactive({
  tect_end_date: "",
  tect_strt_date: "",
});

let lastSuccessfulForm = JSON.stringify(queryForm);

const formData = reactive({
  id: "",
  tect_strt_date: "",
  tect_end_date: "",
  tgt_p_comp_rate_topl: "",
  tgt_p_comp_rate_lowl: "",
  quat_rew_draw_rati: "",
  upd_time: "",
});

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: "请选择生效开始日期", trigger: "change" },
  ],
  tect_end_date: [
    { required: true, message: "请选择生效结束日期", trigger: "change" },
  ],
  tgt_p_comp_rate_topl: [
    { required: true, message: "请输入目标利润完成率上限", trigger: "blur" },
  ],
  tgt_p_comp_rate_lowl: [
    { required: true, message: "请输入目标利润完成率下限", trigger: "blur" },
  ],
  quat_rew_draw_rati: [
    { required: true, message: "请输入季度绩效奖金计提比例", trigger: "blur" },
  ]
};

// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const loading = ref(false);
// 表格数据
const tableData = ref([]);
const urlParams = getUrlParams();
// 对话框相关
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref();
const isEdit = ref(false);

// 历史记录对话框
const historyDialogVisible = ref(false);
const historyData = ref([]);

// 初始化数据
onMounted(() => {
  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery();

});


// 查询
const handleQuery = () => {
  loading.value = true;

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  const offset = (currentPage.value - 1) * pageSize.value;
  const limit = pageSize.value;

  const filters = {};
  filters.last_flag = `eq.1`;
  if (queryForm.tect_strt_date) {
    filters.tect_strt_date = `eq.${queryForm.tect_strt_date}`;
  }
  if (queryForm.tect_end_date) {
    filters.tect_end_date = `eq.${queryForm.tect_end_date}`;
  }
  //调用接口
  const config = {
    params: {
      ...filters,
    },
    headers: {
      "Accept-Profile": "mkt_base",
      Range: `${offset}-${offset + limit - 1}`,
      Accept: "application/json",
    },
  };
  http
    .get("/brch_rew_draw_rati_mtc", {}, config)
    .then((response) => {
      tableData.value = response.data;
      tableData.value.forEach(item=>{
        const data = new Decimal(item.tgt_p_comp_rate_topl).times(100).toNumber()
        item.tgt_p_comp_rate_topl =  data +'%'
        const data1 = new Decimal(item.tgt_p_comp_rate_lowl).times(100).toNumber()
        item.tgt_p_comp_rate_lowl =  data1 +'%'
        item.tgt_p_comp_rate = "["+ data1 +'%' +"," + data +'%'+")"
        const data2 = new Decimal(item.quat_rew_draw_rati).times(100).toNumber()
        item.quat_rew_draw_rati =  data2 +'%'
      })
      totalCount.value = response.total || 0;
      loading.value = false;
    })
    .catch((error) => {
      console.error("API请求失败:", error);
      ElMessage.error("获取数据失败");
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false;
    });
};

// 新增
const handleAdd = () => {
  dialogTitle.value = "新增分支机构绩效奖金计提比例";
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 修改
const handleEdit = (row) => {
  dialogTitle.value = "修改分支机构绩效奖金计提比例";
  isEdit.value = true;
  resetForm();
  // 填充表单数据
  row.tgt_p_comp_rate_topl = parseFloat(row.tgt_p_comp_rate_topl)
  row.tgt_p_comp_rate_lowl = parseFloat(row.tgt_p_comp_rate_lowl)
  //row.quat_rew_draw_rati = parseFloat(row.quat_rew_draw_rati)
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  await ElMessageBox.confirm("确认删除该条记录？", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });

  const requestData = {
    i_request: {
      optionflg: "3",
      oacode: urlParams.oacode,
      uuid: row.uuid,
    },
  };
  const response = await http.post(
    "/rpc/p_brch_exam_indx_wght_set_e",
    requestData,
    {
      headers: {
        "Content-Profile": "mkt_base",
        "Content-Type": "application/json",
      },
    }
  );
  // 修改：直接检查响应数据中的 o_status 字段
  if (response.data && response.data.o_status === 0) {
    ElMessage.success("删除成功");
    handleQuery(); // 重新查询数据
  } else {
    ElMessage.error(response.data?.o_msg || "删除失败");
  }
};

// 查看历史记录
const handleHistory = (row) => {
  const config = {
    headers: {
      "Accept-Profile": "mkt_base",
      Prefer: "count=exact",
    },
    params: {
      tgt_p_comp_rate_topl: "eq." + parseFloat(row.tgt_p_comp_rate_topl)/100,
      tgt_p_comp_rate_lowl: "eq." + parseFloat(row.tgt_p_comp_rate_lowl)/100,
    },
  };
  // 模拟获取历史记录
  http.get("/brch_rew_draw_rati_mtc", {}, config).then((response) => {
    historyData.value = response.data;
    historyData.value.forEach(item=>{
      const data = new Decimal(item.tgt_p_comp_rate_topl).times(100).toNumber()
      item.tgt_p_comp_rate_topl =  data +'%'
      const data1 = new Decimal(item.tgt_p_comp_rate_lowl).times(100).toNumber()
      item.tgt_p_comp_rate_lowl =  data1 +'%'
      const data2 = new Decimal(item.quat_rew_draw_rati).times(100).toNumber()
      item.quat_rew_draw_rati =  data2 +'%'
    })
    historyDialogVisible.value = true;
  });
};

// 导出
const handleExport = () => {
  ElMessage.info("正在导出数据，请稍候...");
  const filters = {};
  filters.last_flag = `eq.1`;
  if (queryForm.brch_grad) {
    filters.brch_grad = `like.*${queryForm.brch_grad}*`;
  }
  if (queryForm.indx_cd) {
    filters.indx_cd = `like.*${queryForm.indx_cd}*`;
  }
  const config = {
    headers: {
      "Accept-Profile": "mkt_base",
      "Content-Type": "application/json",
    },
    params: {
      ...filters,
    },
  };
  // 获取所有数据
  http.get("brch_exam_indx_wght_set_view", {}, config).then((response) => {
    const allData = response.data;
    if (allData.length === 0) {
      ElMessage.warning("没有数据可导出");
    }
    // 准备导出数据
    const exportData = [
      // 表头
      [
        "生效开始日期",
        "生效结束日期",
        "分支机构等级",
        "指标代码",
        "指标名称",
        "考核指标权重",
        "创建时间",
        "创建人",
        "更新时间",
        "更新人",
        "最新标识",
      ],
      // 数据行
      ...allData.map((item) => [
        item.tect_strt_date,
        item.tect_end_date,
        item.brch_grad,
        item.indx_cd,
        item.indx_name,
        item.exam_indx_wght,
        item.crt_time || "",
        item.creator || "",
        item.upd_time || "",
        item.upd_oper || "",
        item.last_flag || "",
      ]),
    ];
    // 创建工作簿
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "分支机构考核指标权重设置");

    // 生成文件名
    const now = new Date();
    const fileName = `分支机构考核指标权重设置表_${now.getFullYear()}${String(
      now.getMonth() + 1
    ).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}.xlsx`;

    // 下载文件
    XLSX.writeFile(wb, fileName);

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`);
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      const top = formData.tgt_p_comp_rate_topl;
      const low = formData.tgt_p_comp_rate_lowl;
      const rati = formData.quat_rew_draw_rati;
      console.log(rati)
      if (low > top) {
        ElMessage.error("下限值不得大于上限值!");
        return;
      }
      if (
          top === null ||
          top.toString().includes(".") ||
          top < 0 ||
          top > 100 ||
          low === null ||
          low.toString().includes(".") ||
          low < 0 ||
          low > 100 ||
          rati === null ||
          rati.toString().includes(".") ||
          rati.toString().includes("%") ||
          rati < 0 ||
          rati > 100
      ) {
        ElMessage.error("请输入0-100的正整数!");
        return;
      }
      const requestData = {
        i_request: {
          optionflg: isEdit.value ? "2" : "1",
          oacode: urlParams.oacode,
          tect_strt_date: formData.tect_strt_date,
          tect_end_date: formData.tect_end_date,
          tgt_p_comp_rate_topl: formData.tgt_p_comp_rate_topl/100,
          tgt_p_comp_rate_lowl: formData.tgt_p_comp_rate_lowl/100,
          quat_rew_draw_rati: formData.quat_rew_draw_rati/100,
          upd_time: format(now(), "yyyy-MM-dd HH:mm:ss"),
        },
      };

      // 如果是编辑，需要添加uuid
      if (isEdit.value && formData.uuid) {
        requestData.i_request.uuid = formData.uuid;
      }

      const response = await http.post(
        "/rpc/p_brch_rew_draw_rati_mtc_e",
        requestData,
        {
          headers: {
            "Accept-Profile": "mkt_base",
            "Content-Type": "application/json",
          },
        }
      );
      if (response.data && response.data.o_status === 0) {
        ElMessage.success(isEdit.value ? "修改成功" : "新增成功");
        dialogVisible.value = false;
        handleQuery(); // 重新查询数据
      } else {
        ElMessage.error(response.data?.o_msg || "操作失败");
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    tect_strt_date: "",
    tect_end_date: "",
    tgt_p_comp_rate_topl: "",
    tgt_p_comp_rate_lowl: "",
    quat_rew_draw_rati: "",
  });
};

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm();
};

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  handleQuery();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  handleQuery();
};
</script>


<style lang="scss" scoped>
/* 状态标签样式 */
.el-tag {
  font-weight: bold;
}
</style>