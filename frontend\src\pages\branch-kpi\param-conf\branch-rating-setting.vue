<template>
  <div class="report-title">
    <h2>分支机构分类评级设置</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="分支机构代码">
              <el-select
                v-model="queryForm.branchCodes"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请选择分支机构"
                :remote-method="remoteSearchBranch"
                clearable
              >
                <el-option
                  v-for="item in branchOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span>{{ item.value }} - {{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

      <!-- 表格区域 -->
      <el-card class="table-card">
        <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto">
          <el-table-column prop="tect_strt_date" label="生效开始日期" />
          <el-table-column prop="tect_end_date" label="生效结束日期" />
          <el-table-column prop="brch_cd" label="分支机构代码" />
          <el-table-column prop="brch_name" label="分支机构名称" />
          <el-table-column prop="clas_rat_grad" label="分类评定等级" />
          <el-table-column prop="crt_time" label="创建时间" />
          <el-table-column prop="upd_time" label="更新时间" />
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button type="primary"
              size="small" @click="handleEdit(scope.row)">修改</el-button>
              <el-button type="success"
              size="small" @click="handleHistory(scope.row)">历史记录</el-button>
              <el-button type="danger"
              size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 新增/修改对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="600px"
        @close="handleDialogClose"
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生效开始日期" prop="tect_strt_date">
                <el-date-picker
                  v-model="formData.tect_strt_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生效结束日期" prop="tect_end_date">
                <el-date-picker
                  v-model="formData.tect_end_date"
                  type="month"
                  placeholder="选择月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="分支机构" prop="brch_cd">
                <el-select
                  v-model="formData.brch_cd"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请输入分支机构代码或名称进行搜索"
                  :remote-method="remoteSearchBranchForForm"
                  :disabled="isEdit"
                  style="width: 100%"
                  @change="handleBranchChange"
                >
                  <el-option
                    v-for="item in formBranchOptions"
                    :key="item.value"
                    :label="`${item.value} - ${item.label}`"
                    :value="item.value"
                  >
                    <span>{{ item.value }} - {{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="分类评定等级" prop="clas_rat_grad">
                <el-select v-model="formData.clas_rat_grad" placeholder="请选择评级" style="width: 100%">
                  <el-option label="A" value="A" />
                  <el-option label="B" value="B" />
                  <el-option label="C" value="C" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="handleSubmit">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 历史记录对话框 -->
      <el-dialog
        v-model="historyDialogVisible"
        title="历史记录"
        width="900px"
      >
        <el-table :data="historyData"
        border
        stripe
        table-layout="auto">
          <el-table-column prop="tect_strt_date" label="生效开始日期" />
          <el-table-column prop="tect_end_date" label="生效结束日期" />
          <el-table-column prop="brch_cd" label="分支机构代码" />
          <el-table-column prop="brch_name" label="分支机构名称" />
          <el-table-column prop="clas_rat_grad" label="分类评定等级" />
          <el-table-column prop="crt_time" label="创建时间" />
          <el-table-column prop="upd_time" label="更新时间" />
        </el-table>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="historyDialogVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'

// 查询表单
const queryForm = reactive({
  branchCodes: []
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || 'current_user',  // 修正：从 URL 中获取 oacode 参数
    roleid: params.get('roleid') || '001'
  }
}

const urlParams = getUrlParams()

// 分支机构选项
const branchOptions = ref([])
// 表单中的分支机构选项（独立管理）
const formBranchOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyData = ref([])

// 表单数据
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  brch_cd: '',
  brch_name: '',
  clas_rat_grad: '',
  uuid: ''
})

// 表单验证规则
const formRules = {
  tect_strt_date: [
    { required: true, message: '请选择生效开始日期', trigger: 'change' }
  ],
  tect_end_date: [
    { required: true, message: '请选择生效结束日期', trigger: 'change' }
  ],
  brch_cd: [
    { required: true, message: '请选择分支机构', trigger: 'change' }
  ],
  clas_rat_grad: [
    { required: true, message: '请选择分类评定等级', trigger: 'change' }
  ]
}



// 获取分支机构选项（查询区域用）
const loadBranchOptions = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_find_dim_org', {}, config)
    const data = response.data || []

    branchOptions.value = data.map(item => ({
      value: item.department_id,
      label: item.department_nam
    }))
  } catch (error) {
    console.error('获取分支机构选项失败:', error)
    // 如果API失败，设置空数组
    branchOptions.value = []
    ElMessage.warning('获取分支机构选项失败')
  }
}

// 获取分支机构选项（表单用）
const loadFormBranchOptions = async () => {
  try {
    const config = {
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/v_find_dim_org', {}, config)
    const data = response.data || []

    formBranchOptions.value = data.map(item => ({
      value: item.department_id,
      label: item.department_nam
    }))
  } catch (error) {
    console.error('获取表单分支机构选项失败:', error)
    // 如果API失败，设置空数组
    formBranchOptions.value = []
    ElMessage.warning('获取分支机构选项失败')
  }
}

// 初始化数据
onMounted(() => {
  // 初始化分支机构选项
  loadBranchOptions()
  loadFormBranchOptions()

  // 初始化表格数据，使用查询方法以应用分页逻辑
  handleQuery()
})

// 远程搜索分支机构（查询区域用）
const remoteSearchBranch = async (query) => {
  if (query) {
    try {
      const config = {
        params: {
          or: `(department_id.ilike.*${query}*,department_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Accept-Profile': 'mkt_base'
        }
      }

      const response = await http.get('/v_find_dim_org', {}, config)
      const data = response.data || []

      branchOptions.value = data.map(item => ({
        value: item.department_id,
        label: item.department_nam
      }))
    } catch (error) {
      console.error('搜索分支机构失败:', error)
    }
  } else {
    loadBranchOptions()
  }
}

// 远程搜索分支机构（表单用）
const remoteSearchBranchForForm = async (query) => {
  if (query) {
    try {
      const config = {
        params: {
          or: `(department_id.ilike.*${query}*,department_nam.ilike.*${query}*)`
        },
        headers: {
          'Accept': 'application/json',
          'Accept-Profile': 'mkt_base'
        }
      }

      const response = await http.get('/v_find_dim_org', {}, config)
      const data = response.data || []

      formBranchOptions.value = data.map(item => ({
        value: item.department_id,
        label: item.department_nam
      }))
    } catch (error) {
      console.error('搜索分支机构失败:', error)
    }
  } else {
    loadFormBranchOptions()
  }
}

// 处理分支机构选择变更
const handleBranchChange = (value) => {
  const selectedBranch = formBranchOptions.value.find(item => item.value === value)
  if (selectedBranch) {
    formData.brch_name = selectedBranch.label
  }
}

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      // 如果有多个分支机构代码，使用in操作符
      const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
      filters.brch_cd = `in.(${branchCodesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Accept-Profile': 'mkt_base'
      }
    }

  http.get('/v_brch_clas_rat_set', {}, config)
    .then(response => {
      tableData.value = response.data || []
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增分支机构分类评级'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改分支机构分类评级'
  isEdit.value = true
  resetForm()

  // 填充表单数据
  Object.assign(formData, row)

  // 确保当前编辑的分支机构在选项中
  const currentBranch = {
    value: row.brch_cd,
    label: row.brch_name
  }

  // 检查是否已存在，如果不存在则添加
  const existingBranch = formBranchOptions.value.find(item => item.value === row.brch_cd)
  if (!existingBranch) {
    formBranchOptions.value.unshift(currentBranch)
  }

  dialogVisible.value = true
}

// 查看历史记录
const handleHistory = async (row) => {
  try {
    // 构建查询参数，获取该分支机构的所有历史记录
    const config = {
      params: {
        brch_cd: `eq.${row.brch_cd}`,
        order: 'crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取历史记录数据（不添加last_flag条件）
    const response = await http.get('/v_brch_clas_rat_set', {}, config)
    const data = response.data || []

    historyData.value = data
    historyDialogVisible.value = true

  } catch (error) {
    console.error('获取历史记录时发生错误:', error)
    ElMessage.error('获取历史记录失败，请检查网络连接')
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.branchCodes && queryForm.branchCodes.length > 0) {
      const branchCodesStr = queryForm.branchCodes.map(code => `"${code}"`).join(',')
      filters.brch_cd = `in.(${branchCodesStr})`
    }

    // 添加last_flag=1条件
    filters.last_flag = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: 'tect_strt_date.desc,crt_time.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/v_brch_clas_rat_set', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      ['生效开始日期', '生效结束日期', '分支机构代码', '分支机构名称', '分类评定等级', '创建时间', '更新时间'],
      // 数据行
      ...allData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.brch_cd || '',
        item.brch_name || '',
        item.clas_rat_grad || '',
        item.crt_time,
        item.upd_time
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 生效开始日期
      { wch: 15 }, // 生效结束日期
      { wch: 15 }, // 分支机构代码
      { wch: 25 }, // 分支机构名称
      { wch: 15 }, // 分类评定等级
      { wch: 20 }, // 创建时间
      { wch: 20 }  // 更新时间
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '分支机构分类评级设置')

    // 生成文件名
    const now = new Date()
    const fileName = `分支机构分类评级设置_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const requestData = {
          i_request: {
            optionflg: isEdit.value ? "2" : "1",
            oacode: urlParams.oacode,
            tect_strt_date: formData.tect_strt_date,
            tect_end_date: formData.tect_end_date,
            brch_cd: formData.brch_cd,
            clas_rat_grad: formData.clas_rat_grad
          }
        }

        // 如果是编辑，需要添加uuid
        if (isEdit.value && formData.uuid) {
          requestData.i_request.uuid = formData.uuid
        }

        const response = await http.post('/rpc/p_brch_clas_rat_set_e', requestData, {
          headers: {
            'Content-Profile': 'mkt_base',
            'Content-Type': 'application/json'
          }
        })

        // 修改：直接检查响应数据中的 o_status 字段
        if (response.data && response.data.o_status === 0) {
          ElMessage.success(isEdit.value ? '修改成功' : '新增成功')
          dialogVisible.value = false
          handleQuery() // 重新查询数据
        } else {
          ElMessage.error(response.data?.o_msg || '操作失败')
        }
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const requestData = {
      i_request: {
        optionflg: "3",
        oacode: urlParams.oacode,
        uuid: row.uuid
      }
    }

    const response = await http.post('/rpc/p_brch_clas_rat_set_e', requestData, {
      headers: {
        'Content-Profile': 'mkt_base',
        'Content-Type': 'application/json'
      }
    })

    // 修改：直接检查响应数据中的 o_status 字段
    if (response.data && response.data.o_status === 0) {
      ElMessage.success('删除成功')
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(response.data?.o_msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    brch_cd: '',
    brch_name: '',
    clas_rat_grad: '',
    uuid: ''
  })
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}
</script>

<style lang="scss" scoped>
/* empty */
</style>
