$--colors: (
  'primary': ('base': #5B7BFB,
  ),
  'success': ('base': #16D585,
  ),
  'warning': ('base': #F4CD33,
  ),
  'danger': ('base': #F05025,
  ),
  'info': ('base': #42b8dd,
  ),
);

// we can add this to custom namespace, default is 'el'
@forward 'element-plus/theme-chalk/src/mixins/config.scss' with ($namespace: 'ep'
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward 'element-plus/theme-chalk/src/common/var.scss' with ( // do not use same name, it will override.
  $colors: $--colors // $button-padding-horizontal: ('default': 50px)
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// custom dark variables
@use './dark.scss';

// // ========== 自定义按钮样式覆盖 ==========

// 添加自定义表格样式
:deep(.ep-table) {

  // 滚动条加粗
  .ep-scrollbar__bar.is-horizontal {
    height: 10px;
    left: 2px;
  }

  // .ep-scrollbar__bar.is-vertical {
  //   top: 2px;
  //   width: 10px;
  // }

  .cell {
    display: inline-block; // 确保 max-width 生效
    white-space: nowrap;
    max-width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  td:not(:last-child) .cell:hover {
    white-space: pre-wrap;
    /* 使用pre-wrap来允许换行但保留空白字符 */
    overflow: visible;
    text-overflow: clip;
    word-break: keep-all;
    /* 尽量保持单词完整，不强制断开 */
    max-width: 400px;
    /* 保持最大宽度不变 */
    width: 100%;
    /* 确保宽度一致 */
    word-wrap: break-word;
    /* 当单词超过容器宽度时允许换行 */
    display: inline-block;
    /* 确保元素可以正确处理宽度 */
  }

  // 表头样式
  th .cell {
    white-space: nowrap !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center;
  }
}

.button-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}


.query-card {
  margin-bottom: 20px;
}

.query-form {
  padding: 10px 0;
}

.report-title { 
  padding: 20px;
  text-align: left;
}