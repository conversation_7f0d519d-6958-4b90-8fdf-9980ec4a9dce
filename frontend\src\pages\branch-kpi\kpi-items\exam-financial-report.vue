<template>
  <div class="report-title">
    <h2>考核财务报表数据</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="统计开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="统计截止日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择截止月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="报表">
              <el-select
                v-model="queryForm.reportType"
                placeholder="请选择报表类型"
                clearable
              >
                <el-option label="利润表" value="利润表" />
                <el-option label="费用表" value="费用表" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
              <el-button
                type="info"
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="日期" fixed="left" />
        <el-table-column prop="rptl" label="报表" fixed="left" />
        <el-table-column prop="index" label="指标" fixed="left" />
        <el-table-column prop="brh_sum" label="分支机构合计" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.brh_sum) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hs" label="洪山营业部" align="right" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hs) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_jh" label="江汉营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_jh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hshi" label="黄石营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hshi) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_qk" label="硚口营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_qk) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sh" label="上海分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sh_mkt4" label="上海分公司市场四部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sh_mkt4) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_bj" label="北京建国门营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_bj) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_gz" label="广州分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_gz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_cd" label="成都营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_cd) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_shbjxl" label="上海北京西路" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_shbjxl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_wc" label="武昌营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_wc) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_cs" label="长沙营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_cs) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_fz" label="福州营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_fz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_sz" label="深圳分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_sz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_bjhdq" label="北京海淀区营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_bjhdq) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_ty" label="太原营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_ty) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_hz" label="杭州营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_hz) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_shzyl" label="上海张杨路营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_shzyl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_zy" label="中原分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_zy) }}
          </template>
        </el-table-column>
        <el-table-column prop="brof_wh" label="武汉分公司" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brof_wh) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_dl" label="大连营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_dl) }}
          </template>
        </el-table-column>
        <el-table-column prop="brh_yc" label="延长营业部" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.brh_yc) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import * as XLSX from 'xlsx'
import { formatNumber } from '~/utils/format'
import http from '~/http/http.js'

const router = useRouter()
const route = useRoute()

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  reportType: '',
  order: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  return {
    oacode: params.get('oacode') || 'current_user',
    roleid: params.get('roleid') || '001',
    goBack: params.get('goback') || false,
  }
}
const urlParams = getUrlParams()

// 返回上一页
const goBack = () => {
  router.back()
}

// 初始化数据
onMounted(() => {
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

  // 构建查询参数
  const filters = {}

  // 添加查询条件
  if (queryForm.startDate && queryForm.endDate) {
    filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
  } else if (queryForm.startDate) {
    filters.data_date = `gte.${queryForm.startDate}`
  } else if (queryForm.endDate) {
    filters.data_date = `lte.${queryForm.endDate}`
  }

  if (queryForm.reportType) {
    filters.rptl = `eq.${queryForm.reportType}`
  }

  // 添加审核状态条件
  filters.audt_relt = 'eq.1'

  // 添加分页参数
  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: queryForm.order || 'data_date.desc,rptl.asc,index.asc'
    },
    headers: {
      'Accept': 'application/json',
      'Range': `${offset}-${offset + limit - 1}`,
      'Accept-Profile': 'mkt_base'
    }
  }

  http.get('/audt_exam_fin_rptl', {}, config)
    .then(response => {
      // 对数据进行汇总处理
      const rawData = response.data || []
      const aggregatedData = aggregateData(rawData)
      tableData.value = aggregatedData
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 数据汇总函数
const aggregateData = (data) => {
  const groupedData = {}

  data.forEach(item => {
    const key = `${item.data_date}_${item.rptl}_${item.index}`

    if (!groupedData[key]) {
      groupedData[key] = {
        data_date: item.data_date,
        rptl: item.rptl,
        index: item.index,
        brh_sum: 0,
        brh_hs: 0,
        brh_jh: 0,
        brh_hshi: 0,
        brh_qk: 0,
        brof_sh: 0,
        brof_sh_mkt4: 0,
        brh_bj: 0,
        brof_gz: 0,
        brh_cd: 0,
        brh_shbjxl: 0,
        brh_wc: 0,
        brh_cs: 0,
        brh_fz: 0,
        brof_sz: 0,
        brh_bjhdq: 0,
        brh_ty: 0,
        brh_hz: 0,
        brh_shzyl: 0,
        brof_zy: 0,
        brof_wh: 0,
        brh_dl: 0,
        brh_yc: 0
      }
    }

    // 汇总各分支机构数据
    groupedData[key].brh_sum += parseFloat(item.brh_sum) || 0
    groupedData[key].brh_hs += parseFloat(item.brh_hs) || 0
    groupedData[key].brh_jh += parseFloat(item.brh_jh) || 0
    groupedData[key].brh_hshi += parseFloat(item.brh_hshi) || 0
    groupedData[key].brh_qk += parseFloat(item.brh_qk) || 0
    groupedData[key].brof_sh += parseFloat(item.brof_sh) || 0
    groupedData[key].brof_sh_mkt4 += parseFloat(item.brof_sh_mkt4) || 0
    groupedData[key].brh_bj += parseFloat(item.brh_bj) || 0
    groupedData[key].brof_gz += parseFloat(item.brof_gz) || 0
    groupedData[key].brh_cd += parseFloat(item.brh_cd) || 0
    groupedData[key].brh_shbjxl += parseFloat(item.brh_shbjxl) || 0
    groupedData[key].brh_wc += parseFloat(item.brh_wc) || 0
    groupedData[key].brh_cs += parseFloat(item.brh_cs) || 0
    groupedData[key].brh_fz += parseFloat(item.brh_fz) || 0
    groupedData[key].brof_sz += parseFloat(item.brof_sz) || 0
    groupedData[key].brh_bjhdq += parseFloat(item.brh_bjhdq) || 0
    groupedData[key].brh_ty += parseFloat(item.brh_ty) || 0
    groupedData[key].brh_hz += parseFloat(item.brh_hz) || 0
    groupedData[key].brh_shzyl += parseFloat(item.brh_shzyl) || 0
    groupedData[key].brof_zy += parseFloat(item.brof_zy) || 0
    groupedData[key].brof_wh += parseFloat(item.brof_wh) || 0
    groupedData[key].brh_dl += parseFloat(item.brh_dl) || 0
    groupedData[key].brh_yc += parseFloat(item.brh_yc) || 0
  })

  return Object.values(groupedData)
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.startDate && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`
    }

    if (queryForm.reportType) {
      filters.rptl = `eq.${queryForm.reportType}`
    }

    // 添加审核状态条件
    filters.audt_relt = 'eq.1'

    const config = {
      params: {
        ...filters,
        order: queryForm.order || 'data_date.desc,rptl.asc,index.asc'
      },
      headers: {
        'Accept': 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/audt_exam_fin_rptl', {}, config)
    const rawData = response.data || []

    if (rawData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 对数据进行汇总处理
    const aggregatedData = aggregateData(rawData)

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '日期', '报表', '指标', '分支机构合计', '洪山营业部', '江汉营业部', '黄石营业部', '硚口营业部',
        '上海分公司', '上海分公司市场四部', '北京建国门营业部', '广州分公司', '成都营业部', '上海北京西路',
        '武昌营业部', '长沙营业部', '福州营业部', '深圳分公司', '北京海淀区营业部', '太原营业部',
        '杭州营业部', '上海张杨路营业部', '中原分公司', '武汉分公司', '大连营业部', '延长营业部'
      ],
      // 数据行
      ...aggregatedData.map(item => [
        item.data_date || '',
        item.rptl || '',
        item.index || '',
        formatNumber(item.brh_sum),
        formatNumber(item.brh_hs),
        formatNumber(item.brh_jh),
        formatNumber(item.brh_hshi),
        formatNumber(item.brh_qk),
        formatNumber(item.brof_sh),
        formatNumber(item.brof_sh_mkt4),
        formatNumber(item.brh_bj),
        formatNumber(item.brof_gz),
        formatNumber(item.brh_cd),
        formatNumber(item.brh_shbjxl),
        formatNumber(item.brh_wc),
        formatNumber(item.brh_cs),
        formatNumber(item.brh_fz),
        formatNumber(item.brof_sz),
        formatNumber(item.brh_bjhdq),
        formatNumber(item.brh_ty),
        formatNumber(item.brh_hz),
        formatNumber(item.brh_shzyl),
        formatNumber(item.brof_zy),
        formatNumber(item.brof_wh),
        formatNumber(item.brh_dl),
        formatNumber(item.brh_yc)
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 12 }, // 日期
      { wch: 15 }, // 报表
      { wch: 25 }, // 指标
      ...Array(23).fill({ wch: 15 }) // 各营业部数据
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '考核财务报表数据查询')

    // 生成文件名
    const now = new Date()
    const fileName = `考核财务报表数据查询_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${aggregatedData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

</script>

<style lang="scss" scoped>
.report-title {
  padding: 20px;

  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
}

.query-card {
  margin-bottom: 20px;

  .query-form {
    .el-form-item {
      margin-bottom: 18px;
    }

    .button-group {
      text-align: center;
      margin-top: 10px;

      .el-button {
        margin: 0 5px;
      }
    }
  }
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: center;
  }
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
      white-space: nowrap;
      vertical-align: middle;
    }
  }

  .el-table__body-wrapper {
    td {
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        overflow: visible;
        white-space: normal;
        word-wrap: break-word;
      }
    }
  }
}
</style>