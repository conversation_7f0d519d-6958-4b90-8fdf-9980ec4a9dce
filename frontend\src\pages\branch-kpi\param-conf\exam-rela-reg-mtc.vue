<template>
  <div class="report-title">
    <h2>绩效考核相关制度维护</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="制度类型">
              <el-input v-model="queryForm.regType" placeholder="请输入制度类型" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="制度名称">
              <el-input v-model="queryForm.regName" placeholder="请输入制度名称" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
      >
        <el-table-column prop="tect_strt_date" label="生效开始日期" />
        <el-table-column prop="tect_end_date" label="生效结束日期" />
        <el-table-column prop="reg_type" label="制度类型" />
        <el-table-column prop="reg_name" label="制度名称" />
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column prop="crt_time" label="创建时间" />
        <el-table-column prop="upd_time" label="更新时间" />
        <el-table-column label="操作" fixed="right" width="180px">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="success" size="small" @click="handleView(scope.row)">查看详情</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="previewDialogVisible" :title="currentRow?.file_name ? currentRow.file_name.slice(0, 20) + (currentRow.file_name.length > 20 ? '...' : '') : '文件预览'" width="60%">
        <!-- <div v-html="previewHtml"></div> -->
        <div ref="previewContainer" class="docx-preview-container"></div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="previewDialogVisible = false">关闭</el-button>
            <el-button type="primary" @click="handleDownload(currentRow)">下载</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/修改对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始日期" prop="tect_strt_date">
              <el-date-picker
                v-model="formData.tect_strt_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束日期" prop="tect_end_date">
              <el-date-picker
                v-model="formData.tect_end_date"
                type="month"
                placeholder="选择月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="制度类型" prop="reg_type">
              <el-input v-model="formData.reg_type" placeholder="请输入制度类型" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="制度名称" prop="reg_name">
              <el-input v-model="formData.reg_name" placeholder="请输入制度名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="上传附件" prop="file_path">
              <el-upload
                ref="uploadRef"
                class="upload-demo"
                drag
                :auto-upload="false"
                :limit="1"
                accept=".docx,.doc"
                :on-change="handleFileChange"
                :file-list="fileList"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传 docx/doc 文件，且不超过 10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
import http from '~/http/http.js'
import { nextTick } from 'vue'
import * as docx from 'docx-preview'

// 查询表单
const queryForm = reactive({
  regType: '',
  regName: ''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const isEdit = ref(false)

// 表单数据
const formData = reactive({
  tect_strt_date: '',
  tect_end_date: '',
  reg_type: '',
  reg_name: '',
  file_path: '',
  file_name: '',
  file_content: null,
  uuid: null
})

// 表单验证规则
const formRules = {
  tect_strt_date: [{ required: true, message: '请选择生效开始日期', trigger: 'change' }],
  tect_end_date: [{ required: true, message: '请选择生效结束日期', trigger: 'change' }],
  reg_type: [{ required: true, message: '请输入制度类型', trigger: 'blur' }],
  reg_name: [{ required: true, message: '请输入制度名称', trigger: 'blur' }],
  file_path: [{ required: true, message: '请上传附件', trigger: 'change' }]
}

// 文件上传相关
const fileList = ref([])

const previewContainer = ref(null)

// 初始化数据
onMounted(() => {
  handleQuery()
})

// 查询
const handleQuery = async () => {
  loading.value = true

  // 构建查询参数
  const filters = {}

  if (queryForm.regType) {
    filters.reg_type = `ilike.*${queryForm.regType}*`
  }

  if (queryForm.regName) {
    filters.reg_name = `ilike.*${queryForm.regName}*`
  }

  const offset = (currentPage.value - 1) * pageSize.value
  const limit = pageSize.value

  const config = {
    params: {
      ...filters,
      order: 'tect_strt_date.desc, crt_time.desc',
      offset: offset,
      limit: limit
    },
    headers: {
      Accept: 'application/json',
      'Accept-Profile': 'mkt_base'
    }
  }

  try {
    const response = await http.get('/exam_rela_reg_mtc', {}, config)
    tableData.value = response.data || []
    totalCount.value = response.total || 0
  } catch (error) {
    console.error('API请求失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增绩效考核相关制度'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 修改
const handleEdit = (row) => {
  dialogTitle.value = '修改绩效考核相关制度'
  isEdit.value = true
  resetForm()
  //Object.assign(formData, row)
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该条记录？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const config = {
      headers: {
        'Content-Profile': 'mkt_base'
      }
    }

    await http.delete(`/exam_rela_reg_mtc?uuid=eq.${row.uuid}`, config)
    ElMessage.success('删除成功')
    handleQuery()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除数据时发生错误:', error)
      ElMessage.error('删除失败，请检查网络连接')
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const config = {
      params: {
        order: 'tect_strt_date.desc, crt_time.desc'
      },
      headers: {
        Accept: 'application/json',
        'Accept-Profile': 'mkt_base'
      }
    }

    const response = await http.get('/exam_rela_reg_mtc', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    const exportData = [
      ['生效开始日期', '生效结束日期', '制度类型', '制度名称', '文件名', '创建时间', '更新时间'],
      ...allData.map(item => [
        item.tect_strt_date || '',
        item.tect_end_date || '',
        item.reg_type || '',
        item.reg_name || '',
        item.file_name || '',
        item.crt_time,
        item.upd_time
      ])
    ]

    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    const colWidths = [
      { wch: 15 },
      { wch: 15 },
      { wch: 20 },
      { wch: 20 },
      { wch: 25 },
      { wch: 20 },
      { wch: 20 }
    ]
    ws['!cols'] = colWidths

    XLSX.utils.book_append_sheet(wb, ws, '绩效考核相关制度')

    const now = new Date()
    const fileName = `绩效考核相关制度_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)
  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 上传文件
const handleFileChange = (file) => {
  formData.file_name = file.name
  formData.file_path = file.raw
  const reader = new FileReader()
  reader.onload = (e) => {
    const buffer = e.target.result;
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64String = btoa(binary);
    formData.file_content = base64String;
  };
  reader.readAsArrayBuffer(file.raw)
}

// 上传文件
const uploadFile = async (param) => {
  const file = param.file
  formData.file_name = file.name
  const reader = new FileReader()
  reader.onload = (e) => {
    formData.file_content = e.target.result
  }
  reader.readAsArrayBuffer(file)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const payload = { ...formData };
        delete payload.uuid;
        if (!payload.tect_strt_date) {
          payload.tect_strt_date = null;
        }
        if (!payload.tect_end_date) {
          payload.tect_end_date = null;
        }

        const config = {
          headers: {
            "Content-Profile": "mkt_base",
            "Content-Type": "application/json",
          },
        };

        if (isEdit.value) {
          await http.patch(
            `/exam_rela_reg_mtc?uuid=eq.${payload.uuid}`,
            payload,
            config
          );
          ElMessage.success("修改成功");
        } else {
          await http.post("/exam_rela_reg_mtc", payload, config);
          ElMessage.success("新增成功");
        }

        dialogVisible.value = false
        handleQuery()
      } catch (error) {
        console.error('提交数据时发生错误:', error)
        ElMessage.error('操作失败，请检查网络连接')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }

  Object.assign(formData, {
    tect_strt_date: '',
    tect_end_date: '',
    reg_type: '',
    reg_name: '',
    file_path: '',
    file_name: '',
    file_content: null,
    uuid: ''
  })

  fileList.value = []
}

// 处理对话框关闭
const handleDialogClose = () => {
  resetForm()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 对话框相关
const previewDialogVisible = ref(false) // 控制预览对话框显示
const previewHtml = ref('') // 用于存储预览的 HTML 内容
const currentRow = ref(null);

// 下载文件
const handleDownload = (row) => {
  const base64Data = row.file_content.split(',')[1] || row.file_content;
  const fileName = row.file_name;

  // Decode Base64 to binary string
  const binaryString = atob(base64Data);

  // Convert binary string to Uint8Array
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob from the Uint8Array
  const blob = new Blob([bytes], { type: 'application/msword' }); // 假设是 Word 文档

  // Create a download link
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;

  // Append the link to the document and trigger the download
  document.body.appendChild(link);
  link.click();

  // Remove the link from the document
  document.body.removeChild(link);
};

// 查看详情
const handleView = async (row) => {
  currentRow.value = row

  if (row.file_content) {
    try {
      // 提取 Base64 数据
      const base64Data = row.file_content.split(',')[1] || row.file_content

      // 解码 Base64 到 Uint8Array
      const binaryString = atob(base64Data)
      const len = binaryString.length
      const bytes = new Uint8Array(len)
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }

      // 创建 ArrayBuffer
      const arrayBuffer = bytes.buffer

      // 先打开弹窗
      previewDialogVisible.value = true

      // 等待 DOM 更新
      await nextTick()

      const container = previewContainer.value
      if (!container) {
        throw new Error('previewContainer 未绑定到 DOM')
      }

      container.innerHTML = '' // 清空旧内容

      // 使用 docx-preview 渲染文档
      docx.renderAsync(arrayBuffer, container).then(() => {
        console.log('文档渲染成功')
      })

    } catch (error) {
      console.error('转换失败', error)
      ElMessage.error('文件转换或预览失败，请检查文件格式或内容。')
    }
  } else {
    ElMessage.warning('未上传附件')
  }
}
</script>

<style lang="scss" scoped>
/* empty */

.docx-preview-container {
  max-height: calc(100vh - 200px); // 根据实际布局调整
  overflow-y: auto;
}

.docx-preview-container::-webkit-scrollbar {
  width: 8px;
}

.docx-preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.docx-preview-container {
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.docx-preview p,
.docx-preview h1,
.docx-preview h2,
.docx-preview h3,
.docx-preview h4,
.docx-preview h5,
.docx-preview h6 {
  margin-top: 0;
  margin-bottom: 1em;
}

.docx-preview table {
  border-collapse: collapse;
  width: 100%;
}

.docx-preview td,
.docx-preview th {
  border: 1px solid #ccc;
  padding: 8px;
}

</style>