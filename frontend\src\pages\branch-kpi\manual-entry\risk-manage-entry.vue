<template>
  <div class="report-title">
    <h2>风险管理数据录入</h2>

    <!-- 查询区域 -->
    <el-card class="query-card">
      <el-form :model="queryForm" class="query-form" label-position="left">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="queryForm.startDate"
                type="month"
                placeholder="选择开始月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束日期">
              <el-date-picker
                v-model="queryForm.endDate"
                type="month"
                placeholder="选择结束月份"
                format="YYYY-MM"
                value-format="YYYY-MM"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分支机构:">
              <el-select
                v-model="queryForm.brh_cd"
                filterable
                remote
                clearable
                reserve-keyword
                :remote-method="loadDeptList" >
              <el-option
                  v-for="dot in deptOptions"
                  :key="dot.value"
                  :label="dot.label"
                  :value="dot.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核结果">
              <el-select
                v-model="queryForm.audt_relt"
                placeholder="请选择审核结果"
                clearable
              >
                <el-option label="未处理" value="0" />
                <el-option label="通过" value="1" />
                <el-option label="不通过" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="primary" @click="handleDownloadTemplate">下载模板</el-button>
              <el-button type="primary" @click="handleImportData">数据导入</el-button>
              <el-button type="success" @click="handleQuery">查询</el-button>
              <el-button type="danger" @click="handleExport">导出</el-button>
              <el-button 
                type="info" 
                @click="goBack"
                v-if="urlParams.goBack === 'true'"
              >
                返回
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="loading"
        table-layout="auto"
        @sort-change="handleSortChange">
        <el-table-column prop="data_date" label="数据日期" fixed="left" />
        <el-table-column prop="brh_cd" label="分支机构代码"/>
        <el-table-column prop="brh_name" label="分支机构名称"/>
        <el-table-column prop="busi_prsn_cd" label="业务人员代码" />
        <el-table-column prop="busi_prsn_name" label="业务人员名称"/>
        <el-table-column prop="proj_name" label="项目名称" />
        <el-table-column prop="busi_type" label="业务类型"/>
        <el-table-column prop="proj_type" label="项目类型"/>
        <el-table-column prop="proj_stat" label="项目状态" />
        <el-table-column prop="over_assn_busi_type" label="超额分配业务类型"/>
        <el-table-column prop="over_assn_busi_incm" label="超额分配业务收入" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.over_assn_busi_incm) }}
          </template>
        </el-table-column>
        <el-table-column prop="righ_amt" label="权利金" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.righ_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="supt_amt" label="支持金额" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.supt_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="righ_tim_val" label="权利金时间价值" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.righ_tim_val) }}
          </template>
        </el-table-column>
        <el-table-column prop="nmpr_amt" label="名义本金" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.nmpr_amt) }}
          </template>
        </el-table-column>
        <el-table-column prop="incv_plcy" label="激励政策"/>
        <el-table-column prop="proj_assn_net_incm" label="项目分配净收入" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.proj_assn_net_incm) }}
          </template>
        </el-table-column>
        <el-table-column prop="cptl_sale_incm" label="资金类销售收入" align="right">
          <template #default="scope">
            {{ formatNumber(scope.row.cptl_sale_incm) }}
          </template>
        </el-table-column>
        <el-table-column prop="belg_year" label="归属年份" />
        <el-table-column prop="cash_date" label="兑现日期" />
        <el-table-column prop="effh_cnvr_num" label="有效户折算户数" align="right"/>
        <el-table-column prop="creator" label="创建人"/>
        <el-table-column prop="crt_time" label="创建时间"/>
        <el-table-column prop="audt_oper" label="审核人"/>
        <el-table-column prop="audt_time" label="审核时间"/>
        <el-table-column prop="audt_relt" label="审核结果">
          <template #default="scope">
            <el-tag
              v-if="scope.row.audt_relt !== null && scope.row.audt_relt !== undefined"
              :type="getAuditStatusTagType(scope.row.audt_relt)"
              size="small"
            >
              {{ formatAuditResult(scope.row.audt_relt) }}
            </el-tag>
            <span v-else>未处理</span>
          </template>
        </el-table-column>
        <el-table-column prop="audt_no_pass_resn" label="审核不通过原因" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="数据导入"
      width="500px"
      @close="handleImportDialogClose"
    >
      <div class="import-content">
        <el-form label-width="100px">
          <el-form-item label="数据日期" required>
            <el-date-picker
              v-model="importForm.dataDate"
              type="month"
              placeholder="选择数据月份"
              format="YYYY-MM"
              value-format="YYYY-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          :on-change="handleFileChange"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button class="import-confirm-btn" @click="handleImportConfirm" :loading="importLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { formatNumber } from '~/utils/format'
import http from '~/http/http.js'
import { format } from 'date-fns';
import {now} from "@vueuse/core";

const router = useRouter()
const route = useRoute()

// 查询表单
const queryForm = reactive({
  startDate: '',
  endDate: '',
  order: '',
  brh_cd: '',
  audt_relt:''
})

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const loading = ref(false)

// 表格数据
const tableData = ref([])
// 分支机构选项
const deptOptions = ref([])
const projOptions = ref([])
const statusOptions = ref([])
const typeOptions = ref([])
// 导入对话框
const importDialogVisible = ref(false)
const uploadRef = ref()
const fileList = ref([])
const importLoading = ref(false)

// 导入表单
const importForm = reactive({
  dataDate: ''
})

// 获取URL参数
const getUrlParams = () => {
  const params = new URLSearchParams(window.location.search)
  queryForm.startDate = params.get('data_date') || ''
  queryForm.endDate = params.get('data_date') || ''
  return {
    oacode: params.get('oacode') || 'current_user',  // 修正：从 URL 中获取 oacode 参数
    roleid: params.get('roleid') || '001',
    goBack: params.get('goback') || false,
    
  }
}
const urlParams = getUrlParams()

const loadDeptList = async(query) => {
  const config = {
    headers:{
      'Accept-Profile':'mkt_base'
    },
    params:{
      'brh_name': 'like.%'+query+'%',
    }
  }
  http.get('/v_find_dim_org', {}, config).then(response => {
    const data = response.data
    deptOptions.value = data.map(item => ({
      value: item.brh_cd,
      label: item.brh_name
    }))
  })
}

//获取字典
const loadDictList = async () => {
  const filters = {};
  filters.dict_code = `eq.zxkh0007`
  const config = {
    headers: {
      "Accept-Profile": "mkt_base",
    },
    params: {
      ...filters,
    },
  };
  http.get("/v_dictionary_cfg", {}, config).then((response) => {
    const data = response.data;
    projOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  });

  const filters1 = {};
  filters1.dict_code = `eq.zxkh0008`
  const config1 = {
    headers: {
      "Accept-Profile": "mkt_base",
    },
    params: {
      ...filters1,
    },
  };
  http.get("/v_dictionary_cfg", {}, config1).then((response) => {
    const data = response.data;
    statusOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  });

  const filters2 = {};
  filters2.dict_code = `eq.zxkh0009`
  const config2 = {
    headers: {
      "Accept-Profile": "mkt_base",
    },
    params: {
      ...filters2,
    },
  };
  http.get("/v_dictionary_cfg", {}, config2).then((response) => {
    const data = response.data;
    typeOptions.value = data.map((item) => ({
      value: item.item_code,
      label: item.item_value,
    }));
  });
};


// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化审核结果
const formatAuditResult = (result) => {
  // 添加空值处理逻辑
  if (result === null || result === undefined || result === '') {
    return '待审核';
  }
  switch (result) {
    case '0':
    case 0:
      return '未处理'
    case '1':
    case 1:
      return '通过'
    case '2':
    case 2:
      return '不通过'
    default:
      return '未处理'
  }
}

// 获取审核状态标签类型
const getAuditStatusTagType = (status) => {
  switch (status) {
    case '1':
    case 1:
      return 'success'
    case '2':
    case 2:
      return 'danger'
    case '0':
    case 0:
    default:
      return 'warning'
  }
}

// 初始化数据
onMounted(async() => {
  await loadDictList();
  handleQuery()
})

let lastSuccessfulForm = JSON.stringify(queryForm);

// 查询
const handleQuery = async () => {
  loading.value = true

  // 如果条件已变更，强制重置到第一页
  if (JSON.stringify(queryForm) !== lastSuccessfulForm) {
    currentPage.value = 1;
  }

    // 构建查询参数
    const filters = {}

    // 添加查询条件
    if (queryForm.startDate  && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`
    }
    if (queryForm.brh_cd) {
      filters.brh_cd = `eq.${queryForm.brh_cd}`
    }
    if (queryForm.audt_relt) {
      filters.audt_relt = `eq.${queryForm.audt_relt}`
    }

    // 添加分页参数
    const offset = (currentPage.value - 1) * pageSize.value
    const limit = pageSize.value

    const config = {
      params: {
        ...filters,
        order: queryForm.order || 'data_date.desc,batch_num.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Range': `${offset}-${offset + limit - 1}`,
        'Content-Profile': 'mkt_base'
      }
    }

  http.get('/audt_risk_mang', {}, config)
    .then(response => {
      tableData.value = response.data || []
      tableData.value.forEach(item=>{
        item.proj_type = projOptions.value.find(dict=>dict.value === item.proj_type).label
        item.proj_stat = statusOptions.value.find(dict=>dict.value === item.proj_stat).label
        item.over_assn_busi_type = typeOptions.value.find(dict=>dict.value === item.over_assn_busi_type).label
      })
      totalCount.value = response.total || 0
    })
    .catch(error => {
      console.error('API请求失败:', error)
      ElMessage.error('获取数据失败')
    })
    .finally(() => {
      lastSuccessfulForm = JSON.stringify(queryForm);
      loading.value = false
    })
}

// 下载模板
const handleDownloadTemplate = () => {
  // 创建模板数据
  const templateHeaders = [
    '分支机构代码', '分支机构名称', '业务人员代码', '业务人员名称', '项目名称',
    '业务类型', '项目类型', '项目状态', '超额分配业务类型', '超额分配业务收入',
    '权利金', '支持金额','权利金时间价值', '名义本金', '激励政策',
    '项目分配净收入', '资金类销售收入', '归属年份', '兑现日期', '有效户折算户数'
  ]

  // 创建示例数据
  const templateData = [
    templateHeaders,
  ]

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.aoa_to_sheet(templateData)

  // 设置列宽
  const colWidths = templateHeaders.map(() => ({ wch: 15 }))
  ws['!cols'] = colWidths

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '风险管理数据模板')

  // 下载文件
  XLSX.writeFile(wb, '风险管理数据导入模板.xlsx')

  ElMessage.success('模板下载成功')
}

// 数据导入
const handleImportData = () => {
  importDialogVisible.value = true
  fileList.value = []
  importForm.dataDate = ''
}

// 文件选择变化
const handleFileChange = (file) => {
  fileList.value = [file]
}

// 导入确认
const handleImportConfirm = async () => {
  // 验证数据日期
  if (!importForm.dataDate) {
    ElMessage.warning('请选择数据日期')
    return
  }

  // 验证文件
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  importLoading.value = true

  try {
    // 读取Excel文件
    const file = fileList.value[0].raw
    const arrayBuffer = await file.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })

    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // 转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (jsonData.length < 2) {
      ElMessage.error('Excel文件数据格式不正确')
      return
    }

    // 获取表头和数据
    const headers = jsonData[0]
    const dataRows = jsonData.slice(1)

    // 验证表头格式
    const expectedHeaders = [
      '分支机构代码', '分支机构名称', '业务人员代码', '业务人员名称', '项目名称',
      '业务类型', '项目类型', '项目状态', '超额分配业务类型', '超额分配业务收入',
      '权利金', '支持金额','权利金时间价值', '名义本金', '激励政策',
      '项目分配净收入', '资金类销售收入', '归属年份', '兑现日期', '有效户折算户数'
    ]

    // 检查表头是否匹配
    const headerMatches = expectedHeaders.every((header, index) => headers[index] === header)
    if (!headerMatches) {
      ElMessage.error('Excel文件表头格式不正确，请使用标准模板')
      return
    }

    // 生成批次号（时间戳）
    const batchNum = Date.now().toString()
    const currentTime = format(now(), 'yyyy-MM-dd HH:mm:ss')

    // 转换数据格式
    const importData = dataRows.map(row => ({
      batch_num: batchNum,
      data_date: importForm.dataDate,
      brh_cd: row[0] || '',
      brh_name: row[1] || '',
      busi_prsn_cd: row[2] || '',
      busi_prsn_name: row[3] || '',
      proj_name: row[4] || '',
      busi_type: row[5] || '',
      proj_type: projOptions.value.find(dict=>dict.label === row[6]).value,
      proj_stat: statusOptions.value.find(dict=>dict.label === row[7]).value,
      over_assn_busi_type: typeOptions.value.find(dict=>dict.label === row[8]).value,
      over_assn_busi_incm: parseFloat(row[9] || 0),
      righ_amt: parseFloat(row[10] || 0),
      supt_amt: parseFloat(row[11] || 0),
      righ_tim_val: parseFloat(row[12] || 0),
      nmpr_amt: parseFloat(row[13] || 0),
      incv_plcy: row[14] || '',
      proj_assn_net_incm: parseFloat(row[15]) || 0,
      cptl_sale_incm:  parseFloat(row[16]) || 0,
      belg_year: row[17] || '',
      cash_date: row[18] || '',
      effh_cnvr_num: parseFloat(row[19]) || 0,
      creator: urlParams.oacode, 
      crt_time: currentTime
    }))

    // 调用API导入数据
    const response = await http.post('/temp_risk_mang', JSON.stringify(importData), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    })

    // 修改：直接检查响应数据中的 o_status 字段
    if (response.data && response.data.o_status === 0) {
      // 数据导入成功后，调用系统采集数据审核
      try {
        const auditResponse = await http.post('/rpc/check_data', {
          p_imp_table: "temp_risk_mang",
          p_audt_table: "audt_risk_mang",
          p_batch_num: batchNum,
          p_task_desc: "风险管理数据录入",
          p_url: '/branch-kpi/manual-entry/risk-manage-entry' // 使用动态文件名
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Content-Profile': 'mkt_base'
          }
        })

        if (auditResponse.data && auditResponse.data.o_status === 0) {
          ElMessage.success(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表成功`)
        } else if (auditResponse.data && auditResponse.data.o_status === -1) {
          ElMessage.error(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表失败: ${auditResponse.data.o_msg}`)
        } else {
          ElMessage.warning(`数据导入临时表成功，共导入 ${importData.length} 条记录，同步审核表状态未知: ${auditResponse.data?.o_msg || '未知错误'}`)
        }
      } catch (auditError) {
        console.error('系统审核调用失败:', auditError)
        ElMessage.warning(`数据导入成功，但系统审核调用异常`)
      }

      importDialogVisible.value = false
      handleQuery() // 重新查询数据
    } else {
      ElMessage.error(`数据导入失败: ${response.data?.o_msg || '未知错误'}`)
    }

  } catch (error) {
    console.error('导入数据时发生错误:', error)
    ElMessage.error(`数据导入失败: ${error.response?.data?.o_msg || error.message || '请检查文件格式'}`)
  } finally {
    importLoading.value = false
  }
}

// 导入对话框关闭
const handleImportDialogClose = () => {
  fileList.value = []
  importForm.dataDate = ''
  importLoading.value = false
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    // 构建查询参数，获取所有数据（不分页）
    const filters = {}

    // 添加查询条件
    if (queryForm.startDate  && queryForm.endDate) {
      filters.and = `(data_date.gte.${queryForm.startDate},data_date.lte.${queryForm.endDate})`
    } else if (queryForm.startDate) {
      filters.data_date = `gte.${queryForm.startDate}`
    } else if (queryForm.endDate) {
      filters.data_date = `lte.${queryForm.endDate}`
    }
    if (queryForm.brh_cd) {
      filters.brh_cd = `eq.${queryForm.brh_cd}`
    }
    if (queryForm.audt_relt) {
      filters.audt_relt = `eq.${queryForm.audt_relt}`
    }
    const config = {
      params: {
        ...filters,
        order: queryForm.order || 'data_date.desc,batch_num.desc'
      },
      headers: {
        'Accept': 'application/json',
        'Content-Profile': 'mkt_base'
      }
    }

    // 获取所有数据
    const response = await http.get('/audt_risk_mang', {}, config)
    const allData = response.data || []

    if (allData.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }

    // 准备导出数据
    const exportData = [
      // 表头
      [
        '数据日期', '分支机构代码', '分支机构名称', '业务人员代码', '业务人员名称',
        '项目名称', '业务类型', '项目类型', '项目状态', '超额分配业务类型',
        '超额分配业务收入', '权利金', '支持金额','权利金时间价值', '名义本金',
        '激励政策', '项目分配净收入', '资金类销售收入', '归属年份', '兑现日期',
        '有效户折算户数', '创建人', '创建时间','审核人', '审核时间', '审核结果', '审核不通过原因'
      ],
      // 数据行
      ...allData.map(item => [
        item.data_date || '',
        item.brh_cd || '',
        item.brh_name || '',
        item.busi_prsn_cd || '',
        item.busi_prsn_name || '',
        item.proj_name || '',
        item.busi_type || '',
        item.proj_type || '',
        item.proj_stat || '',
        item.over_assn_busi_type || '',
        formatNumber(item.over_assn_busi_incm),
        formatNumber(item.righ_amt),
        formatNumber(item.supt_amt),
        formatNumber(item.righ_tim_val),
        formatNumber(item.nmpr_amt),
        item.incv_plcy || '',
        formatNumber(item.proj_assn_net_incm),
        formatNumber(item.cptl_sale_incm),
        item.belg_year || '',
        item.cash_date || '',
        formatNumber(item.effh_cnvr_num),
        item.creator || '',
        item.crt_time,
        item.audt_oper || '',
        item.audt_time,
        formatAuditResult(item.audt_relt),
        item.audt_no_pass_resn || ''
      ])
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      ...Array(25).fill({ wch: 20 }), // 各营业部数据
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '风险管理数据录入')

    // 生成文件名
    const now = new Date()
    const fileName = `风险管理数据录入${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}.xlsx`

    // 下载文件
    XLSX.writeFile(wb, fileName)

    ElMessage.success(`数据导出成功，共导出 ${allData.length} 条记录`)

  } catch (error) {
    console.error('导出数据时发生错误:', error)
    ElMessage.error('数据导出失败，请检查网络连接')
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  handleQuery()
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  if (prop && order) {
    const sortOrder = order === 'ascending' ? 'asc' : 'desc';
    queryForm.order = `${prop}.${sortOrder}`;
  } else {
    queryForm.order = '';
  }
  handleQuery();
}

</script>

<style lang="scss" scoped>

/* 上传组件样式 */
.upload-demo {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
  height: 180px;
}
</style>
